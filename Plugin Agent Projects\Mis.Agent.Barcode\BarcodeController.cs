﻿using Mis.Shared.Interface;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Barcode
{
    [ApiController]
    [Route("[controller]")]
    [EnableCors("AllowAll")]
    public class BarcodeController : ControllerBase
    {
        private readonly IBarcodeAppService _barcodeAppService;
        //private readonly IPublicBarcodeAppService _publicBarcodeAppService;
        private readonly IScannerAppService _scannerAppService;
        public BarcodeController(IBarcodeAppService barcodeAppService,
            IScannerAppService scannerAppService)
        {
            _barcodeAppService = barcodeAppService;
            _scannerAppService = scannerAppService;
        }

        [HttpGet("ScanAsync")]
        public async Task<IActionResult> ScanAsync()
        {
            try
            {
                var setting = _scannerAppService.GetSetting("IsScanByBarcodeReader");
                bool.TryParse(setting, out bool isScanByBarcodeReader);

                if (isScanByBarcodeReader)
                    return await ScanUsingBarcodeReader();
                else
                    return ScanUsingScannerDevice();
            }
            catch (UnauthorizedAccessException)
            {
                return StatusCode(403, new { success = false, message = "Access denied to the COM port." });
            }
            catch (IOException ex)
            {
                return StatusCode(500, new { success = false, message = "I/O error occurred: " + ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        private async Task<IActionResult> ScanUsingBarcodeReader()
        {
            string comPort = _scannerAppService.GetCOMPort();
            string barcodeUrl = _scannerAppService.GetBarcodeBaseUrl();
            HoneywellModel model = _scannerAppService.GetHoneywellModelEnum();

            _barcodeAppService.PublicInitialize(barcodeUrl, comPort, true);

            await SerialPortManager.Instance.CaptureImageAsync(comPort, model);
            Thread.Sleep(2000);

            string scannedData = SerialPortManager.Instance.LastScannedBase64Data;
            SerialPortManager.Instance.LastScannedBase64Data = null;

            if (string.IsNullOrEmpty(scannedData))
            {
                _barcodeAppService.ShowNotification("Barcode Notification", "Scanning failed. No image data received.");
                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
            }

            _barcodeAppService.ShowNotification("Barcode Notification", "Barcode Capture Image Successfully");
            return Ok(new { success = true, data = scannedData });
        }

        private IActionResult ScanUsingScannerDevice()
        {
            string selectedScanner = _scannerAppService.GetSetting("Scanner");

            if (string.IsNullOrEmpty(selectedScanner))
            {
                return BadRequest(new { success = false, message = "No scanner is configured in the appsettings.json file." });
            }

            var scanners = _scannerAppService.GetAvailableScanners();
            if (scanners == null || !scanners.Any())
            {
                return BadRequest(new { success = false, message = "No scanners found." });
            }

            string scannedData = SerialPortManager.Instance.ScanWithSelectedScanner(selectedScanner);

            if (string.IsNullOrEmpty(scannedData))
            {
                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
            }

            return Ok(new { success = true, data = scannedData });
        }

        [HttpGet("DebugBarcodeReader")]
        public async Task<IActionResult> DebugBarcodeReader()
        {
            try
            {
                Console.WriteLine("=== DEBUG: Starting Barcode Reader Debug Session ===");

                string comPort = _scannerAppService.GetCOMPort();
                string barcodeUrl = _scannerAppService.GetBarcodeBaseUrl();
                HoneywellModel model = _scannerAppService.GetHoneywellModelEnum();

                Console.WriteLine($"[DEBUG] Configuration:");
                Console.WriteLine($"[DEBUG] - COM Port: {comPort}");
                Console.WriteLine($"[DEBUG] - Barcode URL: {barcodeUrl}");
                Console.WriteLine($"[DEBUG] - Honeywell Model: {model}");

                // Test 1: Check COM port availability
                Console.WriteLine($"\n[DEBUG] Step 1: Checking COM port availability...");
                string[] availablePorts = System.IO.Ports.SerialPort.GetPortNames();
                Console.WriteLine($"[DEBUG] Available ports: {string.Join(", ", availablePorts)}");

                if (!availablePorts.Contains(comPort))
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = $"COM port {comPort} not available. Available ports: {string.Join(", ", availablePorts)}"
                    });
                }

                // Test 2: Initialize barcode service
                Console.WriteLine($"\n[DEBUG] Step 2: Initializing barcode service...");
                _barcodeAppService.PublicInitialize(barcodeUrl, comPort, true);

                // Test 3: Check if serial port opens
                Console.WriteLine($"\n[DEBUG] Step 3: Checking serial port connection...");
                if (!SerialPortManager.Instance.IsPortOpen)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Failed to open serial port connection"
                    });
                }

                Console.WriteLine($"[DEBUG] ✓ Serial port opened successfully");

                // Test 4: Send capture command
                Console.WriteLine($"\n[DEBUG] Step 4: Sending capture command...");
                await SerialPortManager.Instance.CaptureImageAsync(comPort, model);

                // Test 5: Wait for response
                Console.WriteLine($"\n[DEBUG] Step 5: Waiting for response (10 seconds)...");
                await Task.Delay(10000);

                // Test 6: Check result
                Console.WriteLine($"\n[DEBUG] Step 6: Checking result...");
                string scannedData = SerialPortManager.Instance.LastScannedBase64Data;
                SerialPortManager.Instance.LastScannedBase64Data = null;

                if (string.IsNullOrEmpty(scannedData))
                {
                    Console.WriteLine($"[DEBUG] ✗ No data received");
                    return BadRequest(new
                    {
                        success = false,
                        message = "No image data received. Check debug console for detailed logs."
                    });
                }

                Console.WriteLine($"[DEBUG] ✓ SUCCESS! Received Base64 data (length: {scannedData.Length})");
                Console.WriteLine($"[DEBUG] First 100 characters: {scannedData.Substring(0, Math.Min(100, scannedData.Length))}...");

                return Ok(new
                {
                    success = true,
                    data = scannedData,
                    message = $"Successfully captured image with {model}. Check console for detailed debug logs.",
                    debugInfo = new
                    {
                        comPort = comPort,
                        model = model.ToString(),
                        dataLength = scannedData.Length
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] ✗ Exception: {ex.Message}");
                Console.WriteLine($"[DEBUG] Stack trace: {ex.StackTrace}");
                return StatusCode(500, new
                {
                    success = false,
                    message = ex.Message,
                    stackTrace = ex.StackTrace
                });
            }
        }

        //[HttpGet("ScanAsync")]
        //public async Task<IActionResult> ScanAsync()
        //{
        //    try
        //    {
        //        //Retrieve the value of IsScanByBarcodeReader from the configuration(appsettings.json)
        //        bool isScanByBarcodeReader = bool.Parse(_scannerAppService.GetSetting("IsScanByBarcodeReader"));
        //        if (isScanByBarcodeReader)
        //        {
        //            //Barcode Reader logic
        //            string comPort = _scannerAppService.GetCOMPort();
        //            string barcodeUrl = _scannerAppService.GetBarcodeBaseUrl();
        //            // Initialize the barcode service
        //            _barcodeAppService.PublicInitialize(barcodeUrl, comPort, true);
        //            // Capture the image
        //           await SerialPortManager.Instance.CaptureImageAsync(comPort);
        //             Thread.Sleep(2000);

        //            string scannedData = SerialPortManager.Instance.LastScannedBase64Data;

        //            SerialPortManager.Instance.LastScannedBase64Data = null;
        //            if (string.IsNullOrEmpty(scannedData))
        //            {
        //                _barcodeAppService.ShowNotification("Barcode Notification", "Scanning failed. No image data received.");
        //                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
        //            }

        //            _barcodeAppService.ShowNotification("Barcode Notification", "Barcode Capture Image Successfully");
        //            return Ok(new { success = true, data = scannedData });
        //        }
        //        else
        //        {
        //            //Non - barcode scanner logic: Get the scanner name from appsettings

        //            string selectedScanner = _scannerAppService.GetSetting("Scanner");

        //            if (string.IsNullOrEmpty(selectedScanner))
        //            {
        //                return BadRequest(new { success = false, message = "No scanner is configured in the appsettings.json file." });
        //            }

        //            //Scanner logic
        //            var scanners = _scannerAppService.GetAvailableScanners();
        //            if (scanners == null || !scanners.Any())
        //            {
        //                return BadRequest(new { success = false, message = "No scanners found." });
        //            }

        //            //Use the selected scanner to perform scanning
        //            string scannedData = SerialPortManager.Instance.ScanWithSelectedScanner(selectedScanner);

        //            if (string.IsNullOrEmpty(scannedData))
        //            {
        //                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
        //            }

        //            return Ok(new { success = true, data = scannedData });
        //        }
        //        return Ok(new { success = true });
        //    }
        //    catch (UnauthorizedAccessException ex)
        //    {
        //        return StatusCode(403, new { success = false, message = "Access denied to the COM port." });
        //    }
        //    catch (IOException ex)
        //    {
        //        return StatusCode(500, new { success = false, message = "I/O error occurred: " + ex.Message });
        //    }
        //    catch (Exception ex)
        //    {
        //        return StatusCode(500, new { success = false, message = ex.Message });
        //    }
        //}
    }
}
