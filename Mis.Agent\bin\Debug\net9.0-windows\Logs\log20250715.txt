2025-07-15 12:37:52.583 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 12:37:52.636 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-15 12:37:52.639 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-15 12:37:52.641 +03:00 [INF] Capture image mode set to: false
2025-07-15 12:37:52.642 +03:00 [INF] Initializing hub connection...
2025-07-15 12:37:54.754 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 12:37:54.845 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 12:37:54.847 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 12:37:54.851 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 12:37:54.852 +03:00 [INF] Hosting environment: Production
2025-07-15 12:37:54.853 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 12:37:54.899 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:37:54.925 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:37:54.943 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:37:54.945 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 52.2971ms
2025-07-15 12:37:57.073 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ZtT3cx7gbXoj0eCxZOpDRg - null null
2025-07-15 12:37:57.078 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:37:57.133 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-15 12:37:57.173 +03:00 [INF] Barcode initialized successfully.
2025-07-15 12:38:08.326 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 12:38:08.326 +03:00 [INF] COM ports populated.
2025-07-15 12:38:09.456 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-15 12:38:09.461 +03:00 [INF] TabPage returned successfully.
2025-07-15 12:38:16.263 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null null
2025-07-15 12:38:16.274 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:38:16.277 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 204 null null 14.5133ms
2025-07-15 12:38:16.286 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:38:16.291 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:38:16.295 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:38:16.300 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:38:16.302 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 15.5527ms
2025-07-15 12:38:17.673 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 12:38:17.731 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 12:38:17.739 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 12:38:17.745 +03:00 [INF] Capture image mode set to: false
2025-07-15 12:38:17.748 +03:00 [INF] Initializing hub connection...
2025-07-15 12:38:18.941 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 12:38:18.982 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 12:38:18.987 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 12:38:18.993 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 12:38:18.994 +03:00 [INF] Hosting environment: Production
2025-07-15 12:38:18.996 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 12:38:19.027 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:38:19.056 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:38:19.063 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:38:19.081 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:38:19.089 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 64.2935ms
2025-07-15 12:38:19.120 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - null null
2025-07-15 12:38:19.128 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:38:19.131 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - 204 null null 11.1421ms
2025-07-15 12:38:19.141 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - null null
2025-07-15 12:38:19.145 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:38:19.147 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:38:19.162 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 12:38:19.165 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - 200 0 application/octet-stream 23.8373ms
2025-07-15 12:38:19.181 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - null null
2025-07-15 12:38:19.185 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:38:19.189 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:38:19.265 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - null 32
2025-07-15 12:38:19.271 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:38:19.272 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:38:19.278 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 12:38:19.280 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - 200 0 text/plain 15.276ms
2025-07-15 12:38:19.283 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 12:38:19.297 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - 200 3 application/octet-stream 116.4055ms
2025-07-15 12:38:19.305 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - null null
2025-07-15 12:38:19.310 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:38:19.312 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:38:19.922 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:38:19.926 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:38:19.928 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:38:19.930 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 8.6147ms
2025-07-15 12:38:21.983 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=-4xr9D51HoWDyCn7TIyeUg - null null
2025-07-15 12:38:21.987 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:38:22.030 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:38:22.097 +03:00 [INF] Barcode initialized successfully.
2025-07-15 12:38:27.557 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 12:38:27.558 +03:00 [INF] COM ports populated.
2025-07-15 12:38:27.559 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-15 12:38:27.560 +03:00 [INF] TabPage returned successfully.
2025-07-15 12:38:36.712 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 12:38:36.713 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - 200 72 application/octet-stream 17408.3079ms
2025-07-15 12:38:36.719 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - null null
2025-07-15 12:38:36.720 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:38:36.721 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - 204 null null 2.0037ms
2025-07-15 12:38:36.723 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - null null
2025-07-15 12:38:36.725 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:38:36.725 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:38:47.988 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-15 12:38:47.991 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:38:47.991 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 3.753ms
2025-07-15 12:38:47.995 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-15 12:38:47.996 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:38:47.997 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-15 12:38:48.005 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-15 12:38:48.902 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.String'.
2025-07-15 12:38:48.905 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 896.998ms
2025-07-15 12:38:48.906 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-15 12:38:48.907 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 200 null text/plain; charset=utf-8 912.0299ms
2025-07-15 12:39:05.674 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:39:05.675 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:39:05.676 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.15ms
2025-07-15 12:39:05.678 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:39:05.680 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:39:05.680 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:39:05.684 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:39:11.139 +03:00 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:39:11.147 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 5461.7896ms
2025-07-15 12:39:11.148 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:39:11.149 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 500 null application/json; charset=utf-8 5471.3833ms
2025-07-15 12:39:55.586 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:39:55.588 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:39:55.588 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.0748ms
2025-07-15 12:39:55.591 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:39:55.594 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:39:55.594 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:39:55.595 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:39:55.597 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:39:55.597 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:39:55.598 +03:00 [INF] Initializing hub connection...
2025-07-15 12:39:57.609 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:39:57.615 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:39:57.618 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:39:57.620 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 11.4835ms
2025-07-15 12:39:57.684 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:39:57.686 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2089.7128ms
2025-07-15 12:39:57.688 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:39:57.689 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2098.3066ms
2025-07-15 12:39:59.317 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:39:59.319 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:39:59.321 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:39:59.322 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:39:59.323 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:39:59.324 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:39:59.324 +03:00 [INF] Initializing hub connection...
2025-07-15 12:39:59.654 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=xXeBmfqt4XbK_ySC-vwQXA - null null
2025-07-15 12:39:59.661 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:39:59.665 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:40:01.362 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:40:01.363 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2040.7015ms
2025-07-15 12:40:01.365 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:40:01.366 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2048.5056ms
2025-07-15 12:40:01.368 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:40:01.370 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:40:01.371 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:40:01.372 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 4.268ms
2025-07-15 12:40:03.397 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=PoCqNjfJbivh4369sgRGpQ - null null
2025-07-15 12:40:03.400 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:40:03.404 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:40:06.724 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 12:40:06.729 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - 200 0 text/plain 90005.7279ms
2025-07-15 12:40:06.739 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - null null
2025-07-15 12:40:06.741 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:40:06.741 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - 204 null null 1.8733ms
2025-07-15 12:40:06.743 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=tLYxIwmHKyV8faxeNXKeHQ - null null
2025-07-15 12:40:06.745 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:40:06.745 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:40:15.110 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:40:15.112 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:40:15.113 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.9803ms
2025-07-15 12:40:15.115 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:40:15.117 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:40:15.118 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:40:15.118 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:40:15.119 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:40:15.120 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:40:15.121 +03:00 [INF] Initializing hub connection...
2025-07-15 12:40:17.134 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:40:17.140 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:40:17.143 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:40:17.145 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 11.1022ms
2025-07-15 12:40:17.206 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:40:17.208 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2089.1423ms
2025-07-15 12:40:17.210 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:40:17.210 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2095.4504ms
2025-07-15 12:40:19.176 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=PzvUizY6oOtWfuWfSw_Nxg - null null
2025-07-15 12:40:19.182 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:40:19.184 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:41:19.933 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 12:41:19.953 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 12:41:19.956 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 12:41:19.958 +03:00 [INF] Capture image mode set to: false
2025-07-15 12:41:19.958 +03:00 [INF] Initializing hub connection...
2025-07-15 12:41:20.697 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 12:41:20.735 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 12:41:20.738 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 12:41:20.739 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 12:41:20.740 +03:00 [INF] Hosting environment: Production
2025-07-15 12:41:20.740 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 12:41:22.171 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:41:22.187 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:41:22.202 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:41:22.204 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 35.8865ms
2025-07-15 12:41:24.298 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=vsqsQB4nuOMwtNTF0UEiCQ - null null
2025-07-15 12:41:24.304 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:41:24.421 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:41:24.476 +03:00 [INF] Barcode initialized successfully.
2025-07-15 12:42:21.348 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:42:21.352 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:42:21.354 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.8657ms
2025-07-15 12:42:21.357 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:42:21.359 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:42:21.359 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:42:21.367 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:42:21.766 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:42:25.483 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:42:25.490 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:42:25.490 +03:00 [INF] Initializing hub connection...
2025-07-15 12:42:28.530 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:42:28.532 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:42:28.533 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:42:28.541 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 17.392ms
2025-07-15 12:42:35.457 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=zexOAYSl5RmLIgdZvT-dHg - null null
2025-07-15 12:42:39.559 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:42:43.571 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 21800.6653ms.
2025-07-15 12:42:43.577 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:43:15.016 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:43:15.018 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:43:15.019 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.6385ms
2025-07-15 12:43:15.021 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:43:15.023 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:43:15.023 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:43:15.024 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:43:15.033 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:43:16.474 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:43:16.483 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:43:16.484 +03:00 [INF] Initializing hub connection...
2025-07-15 12:43:18.528 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:43:18.531 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:43:18.533 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:43:18.574 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 13.2672ms
2025-07-15 12:43:18.581 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3544.1374ms.
2025-07-15 12:43:18.585 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:43:18.589 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3562.9633ms
2025-07-15 12:43:18.591 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:43:18.591 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3570.3943ms
2025-07-15 12:43:20.633 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=7Ck1-ULcRj2fd-LMWG0F_g - null null
2025-07-15 12:43:20.635 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:43:20.645 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:43:43.616 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 12:43:43.648 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 12:43:43.650 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 12:43:43.653 +03:00 [INF] Capture image mode set to: false
2025-07-15 12:43:43.653 +03:00 [INF] Initializing hub connection...
2025-07-15 12:43:44.679 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 12:43:44.718 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 12:43:44.720 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 12:43:44.721 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 12:43:44.722 +03:00 [INF] Hosting environment: Production
2025-07-15 12:43:44.722 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 12:43:45.952 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:43:45.967 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:43:45.979 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:43:45.981 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 33.3922ms
2025-07-15 12:43:47.520 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:43:47.525 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:43:47.527 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.5195ms
2025-07-15 12:43:47.530 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:43:47.532 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:43:47.533 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:43:47.540 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:43:47.936 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:43:47.940 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:43:47.941 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:43:47.942 +03:00 [INF] Initializing hub connection...
2025-07-15 12:43:48.006 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 65.0674ms.
2025-07-15 12:43:48.012 +03:00 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:43:48.028 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 483.9749ms
2025-07-15 12:43:48.031 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:43:48.032 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 500 null application/json; charset=utf-8 501.0795ms
2025-07-15 12:43:48.067 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=M8gSqB4DrYVe8eEXeNJ82Q - null null
2025-07-15 12:43:48.069 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:43:48.105 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:43:48.154 +03:00 [INF] Barcode initialized successfully.
2025-07-15 12:43:50.001 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:43:50.007 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:43:50.008 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:43:50.018 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 17.949ms
2025-07-15 12:43:52.073 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=MCfJcEeMtwnDX6XUmUj3OA - null null
2025-07-15 12:43:52.077 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:43:53.407 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:43:53.409 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:43:53.410 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 3.276ms
2025-07-15 12:43:53.412 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:43:53.415 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:43:53.415 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:43:53.416 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:43:53.425 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:43:53.426 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:43:53.427 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:43:53.428 +03:00 [INF] Initializing hub connection...
2025-07-15 12:43:55.471 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:43:55.480 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:43:55.482 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:43:55.510 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 38.8674ms
2025-07-15 12:43:55.523 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2097.0094ms.
2025-07-15 12:43:55.525 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:43:55.526 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2109.2467ms
2025-07-15 12:43:55.527 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:43:55.528 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2115.8531ms
2025-07-15 12:43:57.532 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=plFLosot9H9FRRgbTyUaiQ - null null
2025-07-15 12:43:57.538 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:43:57.549 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:44:06.199 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:44:06.201 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:44:06.202 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.4246ms
2025-07-15 12:44:06.203 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:44:06.206 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:44:06.206 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:44:06.207 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:44:06.215 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:44:06.217 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:44:06.218 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:44:06.219 +03:00 [INF] Initializing hub connection...
2025-07-15 12:44:08.263 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:44:08.266 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:44:08.268 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:44:08.288 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2070.5376ms.
2025-07-15 12:44:08.288 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 25.1769ms
2025-07-15 12:44:08.290 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:44:08.292 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2084.2776ms
2025-07-15 12:44:08.294 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:44:08.294 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2091.1434ms
2025-07-15 12:44:10.315 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=2UKheo9m-buS7bvVGx2DTA - null null
2025-07-15 12:44:10.319 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:44:10.358 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:44:19.279 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:44:19.282 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:44:19.284 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 4.6412ms
2025-07-15 12:44:19.287 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:44:19.290 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:44:19.290 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:44:19.291 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:44:19.300 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:44:19.301 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:44:19.302 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:44:19.303 +03:00 [INF] Initializing hub connection...
2025-07-15 12:45:00.624 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 12:45:00.645 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 12:45:00.647 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 12:45:00.649 +03:00 [INF] Capture image mode set to: false
2025-07-15 12:45:00.650 +03:00 [INF] Initializing hub connection...
2025-07-15 12:45:01.648 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 12:45:01.708 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 12:45:01.709 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 12:45:01.711 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 12:45:01.712 +03:00 [INF] Hosting environment: Production
2025-07-15 12:45:01.713 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 12:45:02.863 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:45:02.878 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:45:02.893 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:45:02.895 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 35.8464ms
2025-07-15 12:45:04.997 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=lFN-lpbNLqAtWJpCE-l2vA - null null
2025-07-15 12:45:05.001 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:45:05.080 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:45:05.134 +03:00 [INF] Barcode initialized successfully.
2025-07-15 12:45:10.047 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:45:10.051 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:45:10.053 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.1795ms
2025-07-15 12:45:10.057 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:45:10.059 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:45:10.060 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:45:10.085 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:45:10.471 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:45:10.475 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:45:10.476 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:45:10.477 +03:00 [INF] Initializing hub connection...
2025-07-15 12:45:12.526 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:45:12.533 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:45:12.535 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:45:12.587 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 23.8725ms
2025-07-15 12:45:14.918 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ED2lCakkq8ZfJG20Pnyvfg - null null
2025-07-15 12:45:14.923 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:45:14.940 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 4464.2868ms.
2025-07-15 12:45:14.946 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:45:14.963 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 4874.7453ms
2025-07-15 12:45:14.964 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:45:14.965 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 4908.2627ms
2025-07-15 12:45:17.312 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:45:17.314 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:45:17.315 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.6576ms
2025-07-15 12:45:17.316 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:45:17.319 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:45:17.320 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:45:17.320 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:45:17.335 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:45:17.338 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:45:17.339 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:45:17.339 +03:00 [INF] Initializing hub connection...
2025-07-15 12:45:19.384 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:45:19.387 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:45:19.388 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:45:19.408 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 13.1255ms
2025-07-15 12:45:21.995 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=2sIWu6GnLYJLlaIpwT-_eA - null null
2025-07-15 12:45:22.003 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:45:22.012 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:45:22.017 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 4679.4402ms.
2025-07-15 12:45:22.019 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:45:22.021 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 4699.0911ms
2025-07-15 12:45:22.022 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:45:22.023 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 4706.8547ms
2025-07-15 12:47:19.417 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 12:47:19.446 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 12:47:19.448 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 12:47:19.450 +03:00 [INF] Capture image mode set to: false
2025-07-15 12:47:19.451 +03:00 [INF] Initializing hub connection...
2025-07-15 12:47:20.990 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 12:47:21.056 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 12:47:21.060 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 12:47:21.063 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 12:47:21.064 +03:00 [INF] Hosting environment: Production
2025-07-15 12:47:21.065 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 12:47:21.880 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:47:21.908 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:47:21.926 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:47:21.929 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 53.9907ms
2025-07-15 12:47:24.063 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=FcI-o_h79KNiUXsoeut-zw - null null
2025-07-15 12:47:24.071 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:47:24.121 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:47:24.186 +03:00 [INF] Barcode initialized successfully.
2025-07-15 12:47:27.766 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:47:27.771 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:47:27.773 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.2115ms
2025-07-15 12:47:27.776 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:47:27.777 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:47:27.778 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:47:27.785 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:47:28.179 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:47:28.182 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:47:28.183 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:47:28.184 +03:00 [INF] Initializing hub connection...
2025-07-15 12:47:30.235 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:47:30.238 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:47:30.239 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:47:30.249 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 14.3559ms
2025-07-15 12:47:31.265 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3081.5532ms.
2025-07-15 12:47:31.271 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:47:31.288 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3499.3715ms
2025-07-15 12:47:31.290 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:47:31.291 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3514.9953ms
2025-07-15 12:47:32.297 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=lfarq2drDTq7j6oKQCsftA - null null
2025-07-15 12:47:32.302 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:47:38.598 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:47:38.600 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:47:38.601 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.6321ms
2025-07-15 12:47:38.602 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:47:38.605 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:47:38.605 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:47:38.606 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:47:38.616 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:47:38.617 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:47:38.618 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:47:38.619 +03:00 [INF] Initializing hub connection...
2025-07-15 12:47:40.662 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:47:40.670 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:47:40.672 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:47:40.699 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 36.1766ms
2025-07-15 12:47:43.920 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=or5Z7-fcqauXIVAFiqdzdg - null null
2025-07-15 12:47:43.924 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:47:43.932 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:47:43.948 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 5331.2253ms.
2025-07-15 12:47:43.950 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:47:43.951 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 5343.4637ms
2025-07-15 12:47:43.952 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:47:43.953 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 5350.1055ms
2025-07-15 12:48:46.094 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 12:48:46.114 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 12:48:46.116 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 12:48:46.118 +03:00 [INF] Capture image mode set to: false
2025-07-15 12:48:46.119 +03:00 [INF] Initializing hub connection...
2025-07-15 12:48:47.124 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 12:48:47.161 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 12:48:47.163 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 12:48:47.165 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 12:48:47.166 +03:00 [INF] Hosting environment: Production
2025-07-15 12:48:47.166 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 12:48:48.371 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:48:48.388 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:48:48.401 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:48:48.404 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 35.9705ms
2025-07-15 12:48:50.489 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=yi2eZnJboRrBvxO3dz0TKw - null null
2025-07-15 12:48:50.498 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:48:50.574 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:48:50.632 +03:00 [INF] Barcode initialized successfully.
2025-07-15 12:48:54.882 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:48:54.887 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:48:54.888 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.6279ms
2025-07-15 12:48:54.891 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:48:54.893 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:48:54.894 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:48:54.909 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:48:55.263 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:48:55.266 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:48:55.268 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:48:55.269 +03:00 [INF] Initializing hub connection...
2025-07-15 12:48:57.314 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:48:57.321 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:48:57.323 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:48:57.353 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 39.2425ms
2025-07-15 12:49:02.963 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=9KMH1VC2HQtXkfM6gd1Rbw - null null
2025-07-15 12:49:02.967 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:49:02.983 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 7716.2843ms.
2025-07-15 12:49:02.988 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:49:03.005 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 8092.3644ms
2025-07-15 12:49:03.006 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:49:03.007 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 8115.8721ms
2025-07-15 12:49:05.375 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:49:05.377 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:49:05.378 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.9181ms
2025-07-15 12:49:05.381 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:49:05.383 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:49:05.383 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:49:05.384 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:49:05.394 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:49:05.395 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:49:05.396 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:49:05.396 +03:00 [INF] Initializing hub connection...
2025-07-15 12:49:07.440 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:49:07.445 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:49:07.446 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:49:07.454 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 13.9386ms
2025-07-15 12:49:11.193 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=-KOryOFAo5nIb8VXJxD3mw - null null
2025-07-15 12:49:11.196 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:49:11.206 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:49:11.217 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 5822.4192ms.
2025-07-15 12:49:11.219 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:49:11.221 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 5836.2524ms
2025-07-15 12:49:11.222 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:49:11.224 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 5843.112ms
2025-07-15 12:49:26.791 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:49:26.793 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:49:26.793 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.4361ms
2025-07-15 12:49:26.796 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:49:26.799 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:49:26.800 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:49:26.800 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:49:26.809 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:49:26.812 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:49:26.813 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:49:26.814 +03:00 [INF] Initializing hub connection...
2025-07-15 12:49:47.315 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:49:47.317 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:49:47.318 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:49:47.336 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 20.6407ms
2025-07-15 12:49:49.379 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=X9yAs0WY1LmJWxlhnQ8iHA - null null
2025-07-15 12:49:49.384 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:49:49.394 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:49:54.043 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 27231.1466ms.
2025-07-15 12:49:54.045 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:49:54.047 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 27245.2537ms
2025-07-15 12:49:54.048 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:49:54.049 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 27252.5918ms
2025-07-15 12:50:42.943 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 12:50:42.965 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 12:50:42.967 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 12:50:42.970 +03:00 [INF] Capture image mode set to: false
2025-07-15 12:50:42.971 +03:00 [INF] Initializing hub connection...
2025-07-15 12:50:43.958 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 12:50:43.993 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 12:50:43.996 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 12:50:43.997 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 12:50:43.998 +03:00 [INF] Hosting environment: Production
2025-07-15 12:50:43.998 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 12:50:45.167 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:50:45.181 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:50:45.193 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:50:45.195 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 31.2773ms
2025-07-15 12:50:55.880 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 12:50:55.957 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 12:50:55.967 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 12:50:55.974 +03:00 [INF] Capture image mode set to: false
2025-07-15 12:50:55.976 +03:00 [INF] Initializing hub connection...
2025-07-15 12:50:58.086 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 12:50:58.163 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 12:50:58.166 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 12:50:58.169 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 12:50:58.170 +03:00 [INF] Hosting environment: Production
2025-07-15 12:50:58.171 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 12:50:58.521 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:50:58.557 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:50:58.579 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:50:58.582 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 68.0114ms
2025-07-15 12:51:00.390 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:51:00.399 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:51:00.402 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 12.6301ms
2025-07-15 12:51:00.409 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:51:00.412 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:51:00.413 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:51:00.427 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:51:00.746 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=mDqQbNXrRsnx48fqLyN4fA - null null
2025-07-15 12:51:00.749 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:51:00.881 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:51:00.977 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:51:05.019 +03:00 [INF] Barcode initialized successfully.
2025-07-15 12:51:06.137 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 12:51:06.142 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=mDqQbNXrRsnx48fqLyN4fA - 101 null null 5396.3562ms
2025-07-15 12:51:11.257 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:51:11.265 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:51:11.268 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:51:11.309 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 52.1058ms
2025-07-15 12:51:11.329 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 10329.427ms.
2025-07-15 12:51:11.350 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:51:11.375 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 10941.711ms
2025-07-15 12:51:11.378 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:51:11.380 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 10970.6933ms
2025-07-15 12:51:13.361 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=H8U0ZpijZAWvcWC94JVJ0A - null null
2025-07-15 12:51:13.366 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:51:16.840 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:51:16.846 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:51:16.848 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 7.6001ms
2025-07-15 12:51:16.851 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:51:16.858 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:51:16.860 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:51:16.862 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:51:16.879 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:51:21.678 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 12:51:21.683 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=H8U0ZpijZAWvcWC94JVJ0A - 101 null null 8321.8756ms
2025-07-15 12:51:24.239 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 7358.0135ms.
2025-07-15 12:51:24.246 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:51:24.248 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 7384.0801ms
2025-07-15 12:51:24.253 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:51:24.257 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 7405.9301ms
2025-07-15 12:51:24.770 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:51:24.772 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:51:24.775 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:51:24.822 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 51.1876ms
2025-07-15 12:51:26.860 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=r5HSCLE3yME2BP0c755jYQ - null null
2025-07-15 12:51:26.865 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:51:38.155 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:51:38.158 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:51:38.159 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 3.9669ms
2025-07-15 12:51:38.163 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:51:38.167 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:51:38.168 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:51:38.169 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:51:38.182 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:51:41.215 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3031.4157ms.
2025-07-15 12:51:41.217 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:51:41.218 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3047.3686ms
2025-07-15 12:51:41.220 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:51:41.221 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3058.0718ms
2025-07-15 12:52:08.818 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 12:52:08.900 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 12:52:08.911 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 12:52:08.920 +03:00 [INF] Capture image mode set to: false
2025-07-15 12:52:08.922 +03:00 [INF] Initializing hub connection...
2025-07-15 12:52:11.365 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 12:52:11.450 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 12:52:11.454 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 12:52:11.459 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 12:52:11.460 +03:00 [INF] Hosting environment: Production
2025-07-15 12:52:11.461 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 12:52:11.979 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:52:12.024 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:52:12.054 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:52:12.060 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 92.382ms
2025-07-15 12:52:14.201 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=2UORpSuT46ZDtSKmWuuiTg - null null
2025-07-15 12:52:14.209 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:52:14.347 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:52:14.424 +03:00 [INF] Barcode initialized successfully.
2025-07-15 12:53:09.531 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:53:09.537 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:53:09.538 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.7225ms
2025-07-15 12:53:09.542 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:53:09.543 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:53:09.544 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:53:09.552 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:53:09.973 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:53:09.975 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:53:20.986 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:53:21.260 +03:00 [INF] Initializing hub connection...
2025-07-15 12:53:23.945 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:53:23.951 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:53:23.954 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:53:23.984 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 39.0306ms
2025-07-15 12:53:26.012 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=MqVidAN53cROKWU4HbdORQ - null null
2025-07-15 12:53:26.017 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:54:15.784 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:54:15.789 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:54:15.791 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 7.0565ms
2025-07-15 12:54:42.045 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 12:54:42.065 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 12:54:42.068 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 12:54:42.070 +03:00 [INF] Capture image mode set to: false
2025-07-15 12:54:42.071 +03:00 [INF] Initializing hub connection...
2025-07-15 12:54:49.664 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 12:54:49.707 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 12:54:49.709 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 12:54:49.710 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 12:54:49.711 +03:00 [INF] Hosting environment: Production
2025-07-15 12:54:49.712 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 12:54:50.932 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:54:50.948 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:54:50.960 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:54:50.962 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 32.5514ms
2025-07-15 12:54:53.066 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=b010Vgv8lXCZIPFnuXh3_g - null null
2025-07-15 12:54:53.074 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:54:54.953 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:54:55.018 +03:00 [INF] Barcode initialized successfully.
2025-07-15 12:55:01.710 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:55:01.715 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:55:01.717 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 7.4242ms
2025-07-15 12:55:01.722 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:55:01.724 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:55:01.724 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:55:01.740 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:55:02.099 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:55:02.103 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:55:02.103 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:55:02.104 +03:00 [INF] Initializing hub connection...
2025-07-15 12:55:10.729 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:55:10.736 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:55:10.738 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:55:10.751 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 22.4898ms
2025-07-15 12:55:12.809 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Ep5vLWrHgkqB4t8wg6514Q - null null
2025-07-15 12:55:12.815 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:56:52.322 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 12:56:52.461 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 12:56:52.479 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 12:56:52.490 +03:00 [INF] Capture image mode set to: false
2025-07-15 12:56:52.494 +03:00 [INF] Initializing hub connection...
2025-07-15 12:56:58.289 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 12:56:58.393 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 12:56:58.396 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 12:56:58.400 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 12:56:58.402 +03:00 [INF] Hosting environment: Production
2025-07-15 12:56:58.404 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 12:56:58.719 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:56:58.761 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:56:58.788 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:56:58.795 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 83.3582ms
2025-07-15 12:57:00.954 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=mZ8bEh3oSBGqEFkfvQYHaQ - null null
2025-07-15 12:57:00.963 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:57:04.949 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:57:05.007 +03:00 [INF] Barcode initialized successfully.
2025-07-15 12:57:12.171 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:57:12.176 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:57:12.177 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.4268ms
2025-07-15 12:57:12.182 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:57:12.184 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:57:12.185 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:57:12.201 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:57:12.548 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:57:12.552 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:57:12.553 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:57:12.554 +03:00 [INF] Initializing hub connection...
2025-07-15 12:57:22.810 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:57:22.815 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:57:22.816 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:57:22.825 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 20.7083ms
2025-07-15 12:57:22.840 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 10286.662ms.
2025-07-15 12:57:22.846 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:57:22.863 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 10658.5033ms
2025-07-15 12:57:22.864 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:57:22.865 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 10683.6196ms
2025-07-15 12:57:31.964 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=aZqfTDtgJOonGHOKTOm7Dg - null null
2025-07-15 12:57:33.660 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:57:36.207 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:57:36.209 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:57:36.210 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 3.5019ms
2025-07-15 12:57:36.212 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:57:36.214 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:57:36.214 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:57:36.215 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:57:36.229 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:57:36.231 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:57:36.232 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:57:36.233 +03:00 [INF] Initializing hub connection...
2025-07-15 12:57:56.907 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:57:56.913 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:57:56.914 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:57:56.937 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 93.4076ms
2025-07-15 12:57:56.938 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 20706.899ms.
2025-07-15 12:57:56.945 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:57:56.946 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 20729.3933ms
2025-07-15 12:57:56.947 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:57:56.948 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 20736.1076ms
2025-07-15 12:57:58.987 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=LqHEmAUmfDrJcKx2493-Xw - null null
2025-07-15 12:57:58.989 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:58:05.879 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:58:09.656 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:58:09.658 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:58:09.659 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.8704ms
2025-07-15 12:58:09.661 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:58:09.664 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:58:09.665 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:58:09.665 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:58:09.674 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:58:09.675 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:58:09.676 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:58:09.677 +03:00 [INF] Initializing hub connection...
2025-07-15 12:58:16.319 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:58:16.321 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:58:16.322 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:58:16.340 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 12.5202ms
2025-07-15 12:58:16.691 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 7015.6199ms.
2025-07-15 12:58:16.692 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:58:16.693 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 7026.968ms
2025-07-15 12:58:16.694 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:58:16.695 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 7034.1517ms
2025-07-15 12:58:18.377 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=AsM1w4araUdPhfUuUk7Avg - null null
2025-07-15 12:58:18.379 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:58:23.575 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:58:27.312 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:58:27.314 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:58:27.315 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.3674ms
2025-07-15 12:58:27.317 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:58:27.321 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:58:27.322 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:58:27.323 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:58:27.331 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:58:27.334 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:58:27.336 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:58:27.337 +03:00 [INF] Initializing hub connection...
2025-07-15 12:58:30.728 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:58:32.644 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 5309.5739ms.
2025-07-15 12:58:32.644 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:58:35.499 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:58:36.913 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:58:37.257 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 9932.7606ms
2025-07-15 12:58:41.618 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 10889.646ms
2025-07-15 12:58:41.618 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=EULMNjXqt4V-b9zs--6snQ - null null
2025-07-15 12:58:41.622 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:58:41.628 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:58:41.629 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 14311.4919ms
2025-07-15 12:58:42.490 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 12:58:45.336 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:58:45.337 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:58:45.338 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 1.8546ms
2025-07-15 12:58:45.340 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 12:58:45.343 +03:00 [INF] CORS policy execution successful.
2025-07-15 12:58:45.343 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:58:45.344 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 12:58:45.352 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 12:58:45.354 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 12:58:45.355 +03:00 [INF] Capture image mode set to: true
2025-07-15 12:58:45.356 +03:00 [INF] Initializing hub connection...
2025-07-15 12:58:52.335 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 12:59:45.482 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 60127.9382ms.
2025-07-15 12:59:45.529 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 12:59:45.638 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 12:59:45.656 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 12:59:45.678 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 12:59:45.678 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 12:59:45.678 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 12:59:45.678 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 12:59:45.678 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 12:59:45.680 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 60335.7022ms
2025-07-15 12:59:45.681 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 53345.9248ms
2025-07-15 12:59:45.683 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=AsM1w4araUdPhfUuUk7Avg - 101 null null 87306.217ms
2025-07-15 12:59:45.683 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=mZ8bEh3oSBGqEFkfvQYHaQ - 101 null null 164729.9174ms
2025-07-15 12:59:45.686 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=LqHEmAUmfDrJcKx2493-Xw - 101 null null 106699.4025ms
2025-07-15 12:59:45.687 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=aZqfTDtgJOonGHOKTOm7Dg - 101 null null 133723.0408ms
2025-07-15 12:59:45.689 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=EULMNjXqt4V-b9zs--6snQ - 101 null null 64070.2727ms
2025-07-15 12:59:45.689 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 12:59:45.701 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 60361.0113ms
2025-07-15 12:59:47.701 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=0w5N7t29YDvD3zxWgy3kqg - null null
2025-07-15 12:59:47.706 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 12:59:47.716 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:00:05.522 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 13:00:05.600 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 13:00:05.608 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 13:00:05.617 +03:00 [INF] Capture image mode set to: false
2025-07-15 13:00:05.619 +03:00 [INF] Initializing hub connection...
2025-07-15 13:00:07.655 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 13:00:07.739 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 13:00:07.743 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 13:00:07.747 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 13:00:07.750 +03:00 [INF] Hosting environment: Production
2025-07-15 13:00:07.751 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 13:00:08.096 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:00:08.129 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:00:08.172 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:00:08.176 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 86.465ms
2025-07-15 13:00:10.321 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=hRMqCUsIc8LRQSNaSThZwQ - null null
2025-07-15 13:00:10.330 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:00:10.402 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:00:10.477 +03:00 [INF] Barcode initialized successfully.
2025-07-15 13:18:28.193 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 13:18:28.217 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 13:18:28.220 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 13:18:28.225 +03:00 [INF] Capture image mode set to: false
2025-07-15 13:18:28.226 +03:00 [INF] Initializing hub connection...
2025-07-15 13:18:29.119 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 13:18:29.171 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 13:18:29.173 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 13:18:29.176 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 13:18:29.178 +03:00 [INF] Hosting environment: Production
2025-07-15 13:18:29.180 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 13:18:30.477 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:18:30.507 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:18:30.523 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:18:30.527 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 53.4454ms
2025-07-15 13:18:32.638 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ENZ92bTKIKt8p9IvM5pIaA - null null
2025-07-15 13:18:32.648 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:18:32.722 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:18:32.808 +03:00 [INF] Barcode initialized successfully.
2025-07-15 13:18:38.902 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:18:38.908 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:18:38.910 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 7.9555ms
2025-07-15 13:18:38.914 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:18:38.918 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:18:38.920 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:18:38.928 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:18:39.344 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:18:39.348 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:18:39.352 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:18:39.353 +03:00 [INF] Initializing hub connection...
2025-07-15 13:18:42.194 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:18:44.368 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:18:44.370 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:18:45.950 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 3765.1059ms
2025-07-15 13:18:45.950 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 5802.6302ms.
2025-07-15 13:18:49.128 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=lCa3qSSvFAHMGiY8fOgzxQ - null null
2025-07-15 13:18:49.131 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 13:18:49.134 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:18:59.231 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 20296.3155ms
2025-07-15 13:18:59.233 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:18:59.237 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 20322.889ms
2025-07-15 13:19:19.416 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 13:19:19.418 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=lCa3qSSvFAHMGiY8fOgzxQ - 101 null null 30289.3031ms
2025-07-15 13:19:24.512 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:19:24.520 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:19:24.522 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:19:24.523 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 11.5092ms
2025-07-15 13:19:26.570 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=8tKTisd5JHKLNoyhKH7ztA - null null
2025-07-15 13:19:26.577 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:19:50.732 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 13:19:50.734 +03:00 [INF] COM ports populated.
2025-07-15 13:19:50.767 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-15 13:19:50.769 +03:00 [INF] TabPage returned successfully.
2025-07-15 13:19:57.625 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:19:57.629 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:19:57.630 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.3431ms
2025-07-15 13:19:57.632 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:19:57.638 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:19:57.640 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:19:57.642 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:19:57.657 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:19:57.658 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:19:57.660 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:19:57.661 +03:00 [INF] Initializing hub connection...
2025-07-15 13:20:02.083 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:20:02.098 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 4439.3463ms.
2025-07-15 13:20:02.102 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:20:02.110 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 13:20:02.114 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:20:02.137 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 4492.3884ms
2025-07-15 13:20:02.138 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 56.2736ms
2025-07-15 13:20:02.141 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:20:02.146 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 4514.18ms
2025-07-15 13:20:04.163 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=YQeaNzxVyAslL5mlbfxD7g - null null
2025-07-15 13:20:04.170 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:20:04.180 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:20:15.434 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:20:15.440 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:20:15.442 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:20:15.496 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 21.6681ms
2025-07-15 13:20:17.307 +03:00 [INF] Connection id "0HNE3H3P4MILV", Request id "0HNE3H3P4MILV:00000001": the application aborted the connection.
2025-07-15 13:20:17.314 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 13:20:17.316 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=YQeaNzxVyAslL5mlbfxD7g - 101 null null 13153.6729ms
2025-07-15 13:20:17.527 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=6AdZ1BeoXZ6FicO2dh-HDQ - null null
2025-07-15 13:20:17.533 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:26:40.250 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 13:26:40.274 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 13:26:40.277 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 13:26:40.280 +03:00 [INF] Capture image mode set to: false
2025-07-15 13:26:40.282 +03:00 [INF] Initializing hub connection...
2025-07-15 13:26:41.089 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 13:26:41.132 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 13:26:41.135 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 13:26:41.138 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 13:26:41.139 +03:00 [INF] Hosting environment: Production
2025-07-15 13:26:41.141 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 13:26:42.504 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:26:42.527 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:26:42.542 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:26:42.544 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 44.0632ms
2025-07-15 13:26:44.665 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=wBsGH17iPMZPNJA-mQ6w6g - null null
2025-07-15 13:26:44.671 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:26:44.710 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:26:44.763 +03:00 [INF] Barcode initialized successfully.
2025-07-15 13:27:27.379 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:27:27.387 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:27:27.390 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 10.8011ms
2025-07-15 13:27:27.393 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:27:27.399 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:27:27.401 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:27:27.409 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:27:27.810 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:27:27.816 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:27:27.819 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:27:27.820 +03:00 [INF] Initializing hub connection...
2025-07-15 13:35:57.892 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 13:35:57.917 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 13:35:57.921 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 13:35:57.925 +03:00 [INF] Capture image mode set to: false
2025-07-15 13:35:57.927 +03:00 [INF] Initializing hub connection...
2025-07-15 13:35:58.932 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 13:35:58.973 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 13:35:58.977 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 13:35:58.979 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 13:35:58.981 +03:00 [INF] Hosting environment: Production
2025-07-15 13:35:58.982 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 13:36:00.169 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:36:00.190 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:36:00.204 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:36:00.226 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 60.3523ms
2025-07-15 13:36:02.333 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=0NZrdZANGHxfDKxo2caiuA - null null
2025-07-15 13:36:02.339 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:36:02.382 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:36:02.438 +03:00 [INF] Barcode initialized successfully.
2025-07-15 13:36:03.409 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:36:03.415 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:36:03.417 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 8.9684ms
2025-07-15 13:36:03.422 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:36:03.424 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:36:03.425 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:36:03.433 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:36:03.817 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:36:03.821 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:36:03.823 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:36:03.825 +03:00 [INF] Initializing hub connection...
2025-07-15 13:36:07.329 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:36:07.334 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:36:07.336 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:36:07.362 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3515.4653ms.
2025-07-15 13:36:07.366 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 46.2358ms
2025-07-15 13:36:07.372 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 13:36:07.402 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3965.1853ms
2025-07-15 13:36:07.406 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:36:07.408 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3986.0638ms
2025-07-15 13:36:09.382 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Cquk7CzMTktboRyWlwlTig - null null
2025-07-15 13:36:09.390 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:36:29.780 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:36:29.785 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:36:29.786 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.0176ms
2025-07-15 13:36:29.789 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:36:29.793 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:36:29.794 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:36:29.796 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:36:29.807 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:36:29.809 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:36:29.810 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:36:29.812 +03:00 [INF] Initializing hub connection...
2025-07-15 13:36:33.039 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:36:33.047 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:36:33.051 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:36:33.060 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3251.6454ms.
2025-07-15 13:36:33.119 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 115.2011ms
2025-07-15 13:36:33.121 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 13:36:33.127 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3328.6455ms
2025-07-15 13:36:33.129 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:36:33.131 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3341.8981ms
2025-07-15 13:36:35.142 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=cISNlzEELTWbV_FCkiLSPg - null null
2025-07-15 13:36:35.145 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:36:35.156 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:36:41.143 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:36:41.148 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:36:41.150 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.8214ms
2025-07-15 13:36:41.153 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:36:41.158 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:36:41.160 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:36:41.163 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:36:41.173 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:36:41.175 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:36:41.177 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:36:41.178 +03:00 [INF] Initializing hub connection...
2025-07-15 13:36:43.230 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:36:43.238 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:36:43.240 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:36:43.262 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 31.9979ms
2025-07-15 13:36:44.229 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3054.5658ms.
2025-07-15 13:36:44.234 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 13:36:44.237 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3070.9982ms
2025-07-15 13:36:44.241 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:36:44.243 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3090.3487ms
2025-07-15 13:36:45.308 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=TJHlkRfiB_yYX-o5_H7z5A - null null
2025-07-15 13:36:45.323 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:36:45.335 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:37:50.315 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 13:37:50.350 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 13:37:50.355 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 13:37:50.359 +03:00 [INF] Capture image mode set to: false
2025-07-15 13:37:50.360 +03:00 [INF] Initializing hub connection...
2025-07-15 13:37:51.920 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 13:37:51.991 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 13:37:52.013 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 13:37:52.019 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 13:37:52.022 +03:00 [INF] Hosting environment: Production
2025-07-15 13:37:52.023 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 13:37:52.675 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:37:52.707 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:37:52.729 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:37:52.736 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 66.6423ms
2025-07-15 13:37:54.894 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=QFkTL7rIvwPA3N7Dp1NgwA - null null
2025-07-15 13:37:54.903 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:37:54.954 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:37:55.019 +03:00 [INF] Barcode initialized successfully.
2025-07-15 13:38:12.933 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:38:12.947 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:38:12.949 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 16.0445ms
2025-07-15 13:38:12.953 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:38:12.955 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:38:12.956 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:38:12.964 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:38:13.327 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:38:13.330 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:38:13.332 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:38:13.333 +03:00 [INF] Initializing hub connection...
2025-07-15 13:38:16.868 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:38:16.874 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:38:16.876 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:38:16.900 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 32.9905ms
2025-07-15 13:38:16.905 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3573.2195ms.
2025-07-15 13:38:16.913 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 13:38:16.930 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3959.9872ms
2025-07-15 13:38:16.936 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:38:16.941 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3987.9607ms
2025-07-15 13:38:18.941 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=P0z-rrbwispsi3VkRL1LpQ - null null
2025-07-15 13:38:18.953 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:38:55.857 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:38:55.861 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:38:55.862 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.3968ms
2025-07-15 13:38:55.864 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:38:55.870 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:38:55.871 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:38:55.873 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:38:55.885 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:38:55.887 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:38:55.889 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:38:55.890 +03:00 [INF] Initializing hub connection...
2025-07-15 13:39:06.672 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:39:06.677 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 10790.2208ms.
2025-07-15 13:39:06.688 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:39:06.690 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 13:39:06.691 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:39:06.714 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 10837.2646ms
2025-07-15 13:39:06.715 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 43.1669ms
2025-07-15 13:39:06.717 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:39:06.721 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 10857.13ms
2025-07-15 13:39:08.760 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=oHeRXwZb6H5xDSFaWAYiZQ - null null
2025-07-15 13:39:08.764 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:39:08.774 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:39:09.945 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:39:09.949 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:39:09.950 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.257ms
2025-07-15 13:39:09.953 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:39:09.958 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:39:09.959 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:39:09.961 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:39:09.971 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:39:09.972 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:39:09.974 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:39:09.975 +03:00 [INF] Initializing hub connection...
2025-07-15 13:40:09.874 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 13:40:09.896 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 13:40:09.899 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 13:40:09.906 +03:00 [INF] Capture image mode set to: false
2025-07-15 13:40:09.907 +03:00 [INF] Initializing hub connection...
2025-07-15 13:40:10.849 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 13:40:10.890 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 13:40:10.893 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 13:40:10.897 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 13:40:10.898 +03:00 [INF] Hosting environment: Production
2025-07-15 13:40:10.899 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 13:40:12.130 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:40:12.151 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:40:12.163 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:40:12.166 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 39.6ms
2025-07-15 13:40:14.254 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=rZPR6kPsQpm2Z3lsHKeGKg - null null
2025-07-15 13:40:14.262 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:40:14.304 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:40:14.367 +03:00 [INF] Barcode initialized successfully.
2025-07-15 13:40:20.023 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:40:20.031 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:40:20.033 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 10.1702ms
2025-07-15 13:40:20.037 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:40:20.042 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:40:20.044 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:40:20.053 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:40:20.506 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:40:20.513 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:40:20.515 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:40:20.516 +03:00 [INF] Initializing hub connection...
2025-07-15 13:40:23.127 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:40:23.134 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:40:23.137 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:40:23.169 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 42.8683ms
2025-07-15 13:40:23.590 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3075.3996ms.
2025-07-15 13:40:23.644 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 13:40:23.662 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3603.0089ms
2025-07-15 13:40:23.666 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:40:23.667 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3630.4927ms
2025-07-15 13:40:25.203 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=J8fDSn55gXLe3ZeA9juOCg - null null
2025-07-15 13:40:25.211 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:42:11.003 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 13:42:11.066 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 13:42:11.076 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 13:42:11.082 +03:00 [INF] Capture image mode set to: false
2025-07-15 13:42:11.084 +03:00 [INF] Initializing hub connection...
2025-07-15 13:42:12.900 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 13:42:12.971 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 13:42:12.974 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 13:42:12.978 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 13:42:12.980 +03:00 [INF] Hosting environment: Production
2025-07-15 13:42:12.984 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 13:42:13.520 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:42:13.555 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:42:13.576 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:42:13.582 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 67.4416ms
2025-07-15 13:42:15.702 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ylGFXAS35GonHtF1yMzqWQ - null null
2025-07-15 13:42:15.711 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:42:15.769 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:42:15.837 +03:00 [INF] Barcode initialized successfully.
2025-07-15 13:42:19.337 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:42:19.344 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:42:19.347 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 9.6935ms
2025-07-15 13:42:19.352 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:42:19.356 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:42:19.358 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:42:19.368 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:42:19.741 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:42:19.746 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:42:19.747 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:42:19.748 +03:00 [INF] Initializing hub connection...
2025-07-15 13:42:22.684 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:42:22.691 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:42:22.694 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:42:22.744 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 73.5024ms
2025-07-15 13:42:22.843 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3093.7049ms.
2025-07-15 13:42:22.859 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 13:42:22.906 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3530.4955ms
2025-07-15 13:42:22.913 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:42:22.915 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3563.5134ms
2025-07-15 13:42:24.761 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Q78nrUFpK0Ng8_ornXKQVg - null null
2025-07-15 13:42:24.770 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:48:49.629 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 13:48:49.653 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 13:48:49.658 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 13:48:49.660 +03:00 [INF] Capture image mode set to: false
2025-07-15 13:48:49.661 +03:00 [INF] Initializing hub connection...
2025-07-15 13:48:50.704 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 13:48:50.745 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 13:48:50.749 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 13:48:50.753 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 13:48:50.754 +03:00 [INF] Hosting environment: Production
2025-07-15 13:48:50.756 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 13:48:51.842 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:48:51.863 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:48:51.875 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:48:51.878 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 38.2395ms
2025-07-15 13:48:54.008 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=JcJQBUg2qGGDUDpLok8MhA - null null
2025-07-15 13:48:54.016 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:48:54.054 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:48:54.114 +03:00 [INF] Barcode initialized successfully.
2025-07-15 13:48:59.915 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:48:59.922 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:48:59.925 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 9.8068ms
2025-07-15 13:48:59.932 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:48:59.936 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:48:59.937 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:48:59.965 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:49:00.378 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:49:00.381 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:49:00.382 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:49:00.383 +03:00 [INF] Initializing hub connection...
2025-07-15 13:49:06.775 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:49:06.781 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 6398.6924ms.
2025-07-15 13:49:06.783 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:49:06.787 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:49:06.830 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 91.607ms
2025-07-15 13:49:06.831 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 13:49:06.852 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 6882.7515ms
2025-07-15 13:49:06.856 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:49:06.857 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 6924.9519ms
2025-07-15 13:49:08.851 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=13duuwy6whT7Ib4xwfL0zQ - null null
2025-07-15 13:49:08.858 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:49:14.613 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:49:14.617 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:49:14.619 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.5654ms
2025-07-15 13:49:14.622 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:49:14.626 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:49:14.627 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:49:14.629 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:49:14.640 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:49:14.642 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:49:14.644 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:49:14.645 +03:00 [INF] Initializing hub connection...
2025-07-15 13:49:16.687 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:49:16.693 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:49:16.695 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:49:16.720 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 33.9627ms
2025-07-15 13:49:17.709 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3067.6857ms.
2025-07-15 13:49:17.711 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 13:49:17.712 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3080.0697ms
2025-07-15 13:49:17.713 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:49:17.728 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3106.4045ms
2025-07-15 13:49:18.748 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=B77aMs7ecEpK3zU5IyxZAg - null null
2025-07-15 13:49:18.760 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:49:18.780 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:50:47.054 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 13:50:47.146 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 13:50:47.157 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 13:50:47.167 +03:00 [INF] Capture image mode set to: false
2025-07-15 13:50:47.169 +03:00 [INF] Initializing hub connection...
2025-07-15 13:50:49.808 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 13:50:49.899 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 13:50:49.904 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 13:50:49.911 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 13:50:49.913 +03:00 [INF] Hosting environment: Production
2025-07-15 13:50:49.914 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 13:50:50.260 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:50:50.296 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:50:50.320 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:50:50.327 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 74.8976ms
2025-07-15 13:50:52.474 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=3KwS_iHvB9VvLiLcVwA-Iw - null null
2025-07-15 13:50:52.484 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 13:50:52.529 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 13:50:52.628 +03:00 [INF] Barcode initialized successfully.
2025-07-15 13:50:52.643 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:50:52.651 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:50:52.654 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 11.6639ms
2025-07-15 13:50:52.662 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 13:50:52.670 +03:00 [INF] CORS policy execution successful.
2025-07-15 13:50:52.673 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:50:52.690 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 13:50:53.025 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 13:50:53.030 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 13:50:53.032 +03:00 [INF] Capture image mode set to: true
2025-07-15 13:50:53.033 +03:00 [INF] Initializing hub connection...
2025-07-15 13:50:55.090 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 13:50:55.099 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 13:50:55.102 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 13:50:55.134 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 44.0214ms
2025-07-15 13:50:56.156 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3122.2028ms.
2025-07-15 13:50:56.163 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 13:50:56.182 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3485.2023ms
2025-07-15 13:50:56.185 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 13:50:56.209 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3547.2813ms
2025-07-15 13:50:57.169 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=c8RMSgkfxrFeKAQucNW-8Q - null null
2025-07-15 13:50:57.179 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:04:23.759 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:04:23.801 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:04:23.806 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:04:23.812 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:04:23.814 +03:00 [INF] Initializing hub connection...
2025-07-15 14:04:25.878 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:04:26.039 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:04:26.045 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:04:26.051 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:04:26.054 +03:00 [INF] Hosting environment: Production
2025-07-15 14:04:26.057 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:04:26.189 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:04:26.244 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:04:26.276 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:04:26.283 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 105.5916ms
2025-07-15 14:04:28.440 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=c64WGf0czY3YI4xuf7Sfrw - null null
2025-07-15 14:04:28.448 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:04:28.575 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:04:28.664 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:08:03.123 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 14:08:03.125 +03:00 [INF] COM ports populated.
2025-07-15 14:08:03.149 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-15 14:08:03.151 +03:00 [INF] TabPage returned successfully.
2025-07-15 14:08:11.372 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:08:11.401 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:08:11.405 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:08:11.409 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:08:11.411 +03:00 [INF] Initializing hub connection...
2025-07-15 14:08:12.224 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:08:12.265 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:08:12.269 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:08:12.271 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:08:12.272 +03:00 [INF] Hosting environment: Production
2025-07-15 14:08:12.275 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:08:13.751 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:08:13.769 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:08:13.783 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:08:13.784 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 35.9275ms
2025-07-15 14:08:15.328 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 14:08:15.334 +03:00 [INF] COM ports populated.
2025-07-15 14:08:15.863 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=f9I1v3txeuZGQDCS9tzqIg - null null
2025-07-15 14:08:15.873 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:08:15.950 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:08:15.975 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:08:16.442 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-15 14:08:16.445 +03:00 [INF] TabPage returned successfully.
2025-07-15 14:08:22.267 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:08:22.290 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-15 14:08:22.293 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-15 14:08:22.297 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:08:22.298 +03:00 [INF] Initializing hub connection...
2025-07-15 14:08:23.011 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:08:23.034 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:08:23.036 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:08:23.039 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:08:23.041 +03:00 [INF] Hosting environment: Production
2025-07-15 14:08:23.043 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:08:24.398 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:08:24.413 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:08:24.419 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:08:24.421 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 25.0947ms
2025-07-15 14:08:26.440 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Qn_RPtw6BJR3U1ZfceZ0kQ - null null
2025-07-15 14:08:26.447 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:08:26.482 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-15 14:08:26.511 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:08:27.243 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:08:27.249 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:08:27.252 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 9.1348ms
2025-07-15 14:08:27.255 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:08:27.259 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:08:27.261 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:08:27.267 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:08:27.634 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: true
2025-07-15 14:08:27.636 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:08:27.637 +03:00 [INF] Initializing hub connection...
2025-07-15 14:08:29.664 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:08:29.669 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:08:29.671 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:08:29.672 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 7.8305ms
2025-07-15 14:08:30.679 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:08:31.701 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=41G9WGOrRUqF9LxKqzYWDw - null null
2025-07-15 14:08:31.711 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:08:37.656 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:08:37.661 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:08:37.662 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.7717ms
2025-07-15 14:08:37.665 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:08:37.671 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:08:37.674 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:08:37.676 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:08:37.680 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: true
2025-07-15 14:08:37.681 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:08:37.683 +03:00 [INF] Initializing hub connection...
2025-07-15 14:08:39.705 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:08:39.711 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:08:39.712 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:08:39.714 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 8.5071ms
2025-07-15 14:08:40.718 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:08:40.723 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3042.0492ms
2025-07-15 14:08:40.729 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:08:40.731 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 200 null application/json; charset=utf-8 3066.4986ms
2025-07-15 14:08:41.758 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=C8EWzyAO5hauq82DLqYoZg - null null
2025-07-15 14:08:41.768 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:08:41.770 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-15 14:18:27.352 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 14:18:27.354 +03:00 [INF] COM ports populated.
2025-07-15 14:18:27.357 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-15 14:18:27.358 +03:00 [INF] TabPage returned successfully.
2025-07-15 14:18:35.577 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 14:18:35.578 +03:00 [INF] COM ports populated.
2025-07-15 14:18:35.580 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-15 14:18:35.581 +03:00 [INF] TabPage returned successfully.
2025-07-15 14:19:22.826 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:19:22.850 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-15 14:19:22.855 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-15 14:19:22.858 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:19:22.859 +03:00 [INF] Initializing hub connection...
2025-07-15 14:19:23.675 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:19:23.716 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:19:23.718 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:19:23.722 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:19:23.723 +03:00 [INF] Hosting environment: Production
2025-07-15 14:19:23.724 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:19:25.077 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:19:25.097 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:19:25.112 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:19:25.141 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 66.903ms
2025-07-15 14:19:27.191 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ymwBic5A01Ce_YYWdvDVhg - null null
2025-07-15 14:19:27.200 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:19:27.249 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-15 14:19:27.296 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:19:42.813 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:19:42.820 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:19:42.824 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 10.6553ms
2025-07-15 14:19:42.827 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:19:42.832 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:19:42.843 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:19:42.851 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:19:43.225 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:19:43.230 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: true
2025-07-15 14:19:43.233 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:19:43.235 +03:00 [INF] Initializing hub connection...
2025-07-15 14:19:43.327 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 96.2123ms.
2025-07-15 14:19:43.333 +03:00 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:19:43.350 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 493.3405ms
2025-07-15 14:19:43.353 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:19:43.356 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 500 null application/json; charset=utf-8 529.2915ms
2025-07-15 14:19:45.360 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:19:45.367 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:19:45.371 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:19:45.384 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 24.8492ms
2025-07-15 14:19:47.427 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=nHXFid2N_79d0hCrzF8xJA - null null
2025-07-15 14:19:47.437 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:19:59.480 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:19:59.485 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:19:59.486 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.1259ms
2025-07-15 14:19:59.488 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:19:59.496 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:19:59.497 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:19:59.499 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:19:59.509 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:19:59.511 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: true
2025-07-15 14:19:59.514 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:19:59.515 +03:00 [INF] Initializing hub connection...
2025-07-15 14:19:59.563 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 51.1819ms.
2025-07-15 14:19:59.567 +03:00 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:19:59.569 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 67.3429ms
2025-07-15 14:19:59.572 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:19:59.573 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 500 null application/json; charset=utf-8 84.4563ms
2025-07-15 14:20:01.597 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:20:01.602 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:20:01.604 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:20:01.623 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 25.3372ms
2025-07-15 14:20:03.658 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ILW3EFemSDQaU-SkmhvnvA - null null
2025-07-15 14:20:03.664 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:20:03.673 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-15 14:20:12.091 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 14:20:12.093 +03:00 [INF] COM ports populated.
2025-07-15 14:20:13.216 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-15 14:20:13.218 +03:00 [INF] TabPage returned successfully.
2025-07-15 14:20:17.951 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:20:17.973 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:20:17.976 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:20:17.979 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:20:17.981 +03:00 [INF] Initializing hub connection...
2025-07-15 14:20:18.644 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:20:18.665 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:20:18.668 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:20:18.671 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:20:18.672 +03:00 [INF] Hosting environment: Production
2025-07-15 14:20:18.673 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:20:20.077 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:20:20.091 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:20:20.097 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:20:20.100 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 23.2407ms
2025-07-15 14:20:22.142 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=kw-sKYHR5pFBvvIwMwi3iw - null null
2025-07-15 14:20:22.154 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:20:22.181 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:20:22.248 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:20:27.344 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:20:27.351 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:20:27.353 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 8.651ms
2025-07-15 14:20:27.356 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:20:27.360 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:20:27.362 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:20:27.369 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:20:27.694 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:20:27.697 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:20:27.698 +03:00 [INF] Initializing hub connection...
2025-07-15 14:20:29.740 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:20:29.748 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:20:29.750 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:20:29.752 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 12.4754ms
2025-07-15 14:20:30.740 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:20:30.803 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3430.3591ms
2025-07-15 14:20:30.806 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:20:30.808 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3451.618ms
2025-07-15 14:20:31.806 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=B1kPPI2N-2f1X275JZXSvA - null null
2025-07-15 14:20:31.812 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:20:39.241 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:20:39.244 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:20:39.246 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 4.9921ms
2025-07-15 14:20:39.248 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:20:39.254 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:20:39.256 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:20:39.257 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:20:39.260 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:20:39.262 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:20:39.263 +03:00 [INF] Initializing hub connection...
2025-07-15 14:20:41.287 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:20:41.295 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:20:41.297 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:20:41.299 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 11.3468ms
2025-07-15 14:20:42.288 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:20:42.291 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3030.9643ms
2025-07-15 14:20:42.346 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:20:42.347 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3098.7428ms
2025-07-15 14:20:43.317 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=8SBj1NgOuR66TUUN6hG7dQ - null null
2025-07-15 14:20:43.324 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:20:43.327 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:24:19.687 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:24:19.709 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:24:19.714 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:24:19.716 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:24:19.717 +03:00 [INF] Initializing hub connection...
2025-07-15 14:24:20.843 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:24:20.909 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:24:20.914 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:24:20.922 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:24:20.929 +03:00 [INF] Hosting environment: Production
2025-07-15 14:24:20.932 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:24:21.920 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:24:21.957 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:24:21.981 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:24:22.013 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 74.8024ms
2025-07-15 14:24:24.150 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=tGPnwagDwRC1PRM9Vea3BA - null null
2025-07-15 14:24:24.159 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:24:24.213 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:24:24.275 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:24:50.988 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:24:50.997 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:24:51.000 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 11.3383ms
2025-07-15 14:24:51.003 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:24:51.007 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:24:51.010 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:24:51.018 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:24:51.398 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:24:51.403 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:24:51.405 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:24:51.408 +03:00 [INF] Initializing hub connection...
2025-07-15 14:26:22.625 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:26:22.651 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:26:22.655 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:26:22.658 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:26:22.659 +03:00 [INF] Initializing hub connection...
2025-07-15 14:26:24.028 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:26:24.099 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:26:24.103 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:26:24.107 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:26:24.109 +03:00 [INF] Hosting environment: Production
2025-07-15 14:26:24.110 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:26:24.918 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:26:24.951 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:26:25.036 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:26:25.041 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 127.9878ms
2025-07-15 14:26:27.202 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=RgM10yksIhs7OziHbnJBCA - null null
2025-07-15 14:26:27.217 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:26:27.264 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:26:27.322 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:26:29.323 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:26:29.338 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:26:29.344 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 20.2785ms
2025-07-15 14:26:29.350 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:26:29.357 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:26:29.359 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:26:29.372 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:26:29.915 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:26:29.922 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:26:29.924 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:26:29.926 +03:00 [INF] Initializing hub connection...
2025-07-15 14:26:32.894 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:26:32.934 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:26:32.936 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:26:32.960 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 123.8751ms
2025-07-15 14:26:33.002 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3079.8007ms.
2025-07-15 14:26:33.055 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:26:33.080 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3702.8609ms
2025-07-15 14:26:33.082 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:26:33.083 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3733.1193ms
2025-07-15 14:26:34.984 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=r9SiiUbV2EGADU9oYHggsw - null null
2025-07-15 14:26:34.990 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:27:05.372 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:27:05.378 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:27:05.379 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.9679ms
2025-07-15 14:27:05.381 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:27:05.388 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:27:05.389 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:27:05.392 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:27:05.403 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:27:05.405 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:27:05.406 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:27:05.408 +03:00 [INF] Initializing hub connection...
2025-07-15 14:28:12.112 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 66668.1431ms.
2025-07-15 14:28:12.121 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:28:12.169 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:28:12.204 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:28:12.207 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 66812.6522ms
2025-07-15 14:28:12.208 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:28:12.268 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:28:12.268 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 14:28:12.269 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 14:28:12.270 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 148.7565ms
2025-07-15 14:28:12.271 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 66889.4246ms
2025-07-15 14:28:12.273 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=r9SiiUbV2EGADU9oYHggsw - 101 null null 97288.2284ms
2025-07-15 14:28:12.273 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=RgM10yksIhs7OziHbnJBCA - 101 null null 105071.6404ms
2025-07-15 14:28:14.300 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=jQBnWok9fPv77ZxxtRyvaw - null null
2025-07-15 14:28:14.307 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:31:27.804 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:31:27.836 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:31:27.844 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:31:27.881 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:31:27.882 +03:00 [INF] Initializing hub connection...
2025-07-15 14:31:28.874 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:31:28.913 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:31:28.915 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:31:28.918 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:31:28.919 +03:00 [INF] Hosting environment: Production
2025-07-15 14:31:28.920 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:31:30.132 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:31:30.150 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:31:30.162 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:31:30.164 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 34.5916ms
2025-07-15 14:31:32.233 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=E9sHCoE8IiX0mlteiKIPKQ - null null
2025-07-15 14:31:32.240 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:31:32.313 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:31:32.371 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:31:38.573 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:31:38.581 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:31:38.583 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 9.8085ms
2025-07-15 14:31:38.587 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:31:38.591 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:31:38.593 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:31:38.609 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:31:39.010 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:31:39.016 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:31:39.018 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:31:39.020 +03:00 [INF] Initializing hub connection...
2025-07-15 14:31:46.411 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:31:46.734 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:31:49.276 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:31:49.327 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 7718.8223ms.
2025-07-15 14:31:49.328 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 2917.3344ms
2025-07-15 14:31:49.339 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:31:49.356 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 10742.4658ms
2025-07-15 14:31:49.360 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:31:49.361 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 10774.4218ms
2025-07-15 14:31:51.362 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=eZ8cZxYjN_pJ1CPuKuwloQ - null null
2025-07-15 14:31:51.374 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:31:56.088 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:31:56.094 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:31:56.095 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 7.3847ms
2025-07-15 14:31:56.098 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:31:56.101 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:31:56.102 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:31:56.104 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:31:56.119 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:31:56.123 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:31:56.125 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:31:56.127 +03:00 [INF] Initializing hub connection...
2025-07-15 14:32:02.365 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:32:39.113 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 42990.5827ms.
2025-07-15 14:32:39.182 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:32:39.190 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:32:39.192 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:32:39.316 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 43209.4322ms
2025-07-15 14:32:39.323 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 37000.7532ms
2025-07-15 14:32:39.328 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:32:39.330 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 14:32:39.330 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 14:32:39.338 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 43240.5292ms
2025-07-15 14:32:39.340 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=eZ8cZxYjN_pJ1CPuKuwloQ - 101 null null 47978.1041ms
2025-07-15 14:32:39.341 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=E9sHCoE8IiX0mlteiKIPKQ - 101 null null 67108.0829ms
2025-07-15 14:32:41.342 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=s9XYmn1JZfowWB6NLfqv1w - null null
2025-07-15 14:32:41.351 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:32:41.362 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:32:49.105 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:32:49.132 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:32:49.136 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:32:49.142 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:32:49.144 +03:00 [INF] Initializing hub connection...
2025-07-15 14:32:50.182 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:32:50.223 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:32:50.227 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:32:50.230 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:32:50.232 +03:00 [INF] Hosting environment: Production
2025-07-15 14:32:50.233 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:32:51.367 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:32:51.386 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:32:51.400 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:32:51.419 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 54.8906ms
2025-07-15 14:32:53.488 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=_TrB4Nh02XnV2fuOkv-qOg - null null
2025-07-15 14:32:53.501 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:32:53.551 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:32:53.606 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:32:57.332 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:32:57.340 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:32:57.343 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 11.4398ms
2025-07-15 14:32:57.347 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:32:57.352 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:32:57.354 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:32:57.362 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:32:57.749 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:32:57.753 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:32:57.755 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:32:57.756 +03:00 [INF] Initializing hub connection...
2025-07-15 14:34:10.857 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:34:10.859 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 73004.5159ms.
2025-07-15 14:34:10.865 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:34:10.867 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 14:34:10.868 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:34:10.892 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=_TrB4Nh02XnV2fuOkv-qOg - 101 null null 77404.1192ms
2025-07-15 14:34:10.893 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 135.0615ms
2025-07-15 14:34:10.894 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:34:10.915 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 73548.3563ms
2025-07-15 14:34:10.917 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:34:10.918 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 73571.4628ms
2025-07-15 14:34:12.913 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=3bDBSz44RRcYansVKaUl7A - null null
2025-07-15 14:34:12.921 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:35:25.008 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:35:25.033 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:35:25.037 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:35:25.040 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:35:25.042 +03:00 [INF] Initializing hub connection...
2025-07-15 14:35:25.984 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:35:26.031 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:35:26.034 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:35:26.037 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:35:26.038 +03:00 [INF] Hosting environment: Production
2025-07-15 14:35:26.040 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:35:27.252 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:35:27.272 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:35:27.287 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:35:27.291 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 42.138ms
2025-07-15 14:35:29.399 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=2yShg1DcNTwLmMPvXNP0Bw - null null
2025-07-15 14:35:29.402 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:35:29.448 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:35:29.504 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:35:35.491 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:35:35.496 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:35:35.498 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 7.3112ms
2025-07-15 14:35:35.503 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:35:35.507 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:35:35.508 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:35:35.521 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:35:35.901 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:35:35.905 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:35:35.907 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:35:35.909 +03:00 [INF] Initializing hub connection...
2025-07-15 14:35:43.621 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:35:43.629 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:35:43.631 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:35:43.662 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 77.3049ms
2025-07-15 14:35:43.669 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 7758.1958ms.
2025-07-15 14:35:43.680 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:35:43.722 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 8194.9023ms
2025-07-15 14:35:43.724 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:35:43.726 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 8223.3889ms
2025-07-15 14:35:45.697 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=40EKJgfqPmQYIst-PSTuLw - null null
2025-07-15 14:35:45.704 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:35:47.328 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:35:47.333 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:35:47.334 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.0216ms
2025-07-15 14:35:47.336 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:35:47.343 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:35:47.344 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:35:47.346 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:35:47.357 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:35:47.362 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:35:47.365 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:35:47.366 +03:00 [INF] Initializing hub connection...
2025-07-15 14:35:58.599 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:35:58.606 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:35:58.608 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:35:58.631 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 11268.8955ms.
2025-07-15 14:35:58.635 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 36.464ms
2025-07-15 14:35:58.638 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:35:58.644 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 11295.246ms
2025-07-15 14:35:58.646 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:35:58.647 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 11310.7944ms
2025-07-15 14:36:00.208 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:36:00.211 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:36:00.213 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.2661ms
2025-07-15 14:36:00.214 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:36:00.220 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:36:00.221 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:36:00.222 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:36:00.231 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:36:00.233 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:36:00.235 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:36:00.236 +03:00 [INF] Initializing hub connection...
2025-07-15 14:36:09.035 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:36:09.035 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=apm95uvocbeSYtMfK43Yaw - null null
2025-07-15 14:36:09.036 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 8803.0336ms.
2025-07-15 14:36:09.041 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:36:09.044 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:36:09.047 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:36:09.049 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:36:09.075 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:36:09.076 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 8852.0229ms
2025-07-15 14:36:09.077 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 41.9606ms
2025-07-15 14:36:09.081 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:36:09.088 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 8873.6962ms
2025-07-15 14:36:11.095 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=NYNS7BekkzQElqiDA8O4-A - null null
2025-07-15 14:36:11.100 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:36:11.110 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:38:42.371 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:38:42.397 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:38:42.403 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:38:42.407 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:38:42.409 +03:00 [INF] Initializing hub connection...
2025-07-15 14:38:43.391 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:38:43.433 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:38:43.437 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:38:43.439 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:38:43.447 +03:00 [INF] Hosting environment: Production
2025-07-15 14:38:43.449 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:38:44.609 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:38:44.625 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:38:44.637 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:38:44.639 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 32.6389ms
2025-07-15 14:38:46.735 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=kfw5XS1gRnfNdrcdjENsmw - null null
2025-07-15 14:38:46.740 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:38:46.777 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:38:46.837 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:38:51.534 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:38:51.541 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:38:51.545 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 11.0882ms
2025-07-15 14:38:51.548 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:38:51.552 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:38:51.554 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:38:51.564 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:38:51.938 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:38:51.944 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:38:51.946 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:38:51.946 +03:00 [INF] Initializing hub connection...
2025-07-15 14:40:11.966 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:40:12.057 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:40:12.067 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:40:12.094 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:40:12.098 +03:00 [INF] Initializing hub connection...
2025-07-15 14:40:13.227 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:40:13.273 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:40:13.277 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:40:13.281 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:40:13.283 +03:00 [INF] Hosting environment: Production
2025-07-15 14:40:13.284 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:40:14.361 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:40:14.382 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:40:14.397 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:40:14.415 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 56.6025ms
2025-07-15 14:40:16.491 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Sb3ClV_7knp1UX8FpcppyA - null null
2025-07-15 14:40:16.499 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:40:16.537 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:40:16.592 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:40:53.174 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:40:53.179 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:40:53.181 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 7.189ms
2025-07-15 14:40:53.186 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:40:53.190 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:40:53.191 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:40:53.200 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:40:53.610 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:40:53.615 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:40:53.620 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:40:53.621 +03:00 [INF] Initializing hub connection...
2025-07-15 14:40:55.667 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:40:55.673 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:40:55.675 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:40:55.689 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 21.9308ms
2025-07-15 14:40:56.693 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 3077.1907ms.
2025-07-15 14:40:56.699 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:40:56.722 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3516.9005ms
2025-07-15 14:40:56.726 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:40:56.727 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 200 null application/json; charset=utf-8 3540.7825ms
2025-07-15 14:40:57.735 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=jfW55K-x65d-EfkzROauLA - null null
2025-07-15 14:40:57.740 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:41:06.724 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:41:06.730 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:41:06.731 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 7.2422ms
2025-07-15 14:41:06.734 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:41:06.740 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:41:06.741 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:41:06.744 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:41:06.755 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:41:06.758 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:41:06.760 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:41:06.762 +03:00 [INF] Initializing hub connection...
2025-07-15 14:41:08.792 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:41:08.797 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:41:08.799 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:41:08.817 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 25.0005ms
2025-07-15 14:41:09.817 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 3058.8816ms.
2025-07-15 14:41:09.872 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:41:09.887 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3140.6514ms
2025-07-15 14:41:09.891 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:41:09.892 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 200 null application/json; charset=utf-8 3157.4233ms
2025-07-15 14:41:10.852 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=y0LovQerEx65nM3oQtl7KQ - null null
2025-07-15 14:41:10.857 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:41:10.868 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:41:14.671 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:41:14.675 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:41:14.677 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.5957ms
2025-07-15 14:41:14.678 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:41:14.682 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:41:14.682 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:41:14.684 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:41:14.696 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:41:14.697 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:41:14.699 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:41:14.700 +03:00 [INF] Initializing hub connection...
2025-07-15 14:41:29.522 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:41:29.578 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:41:29.580 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:41:29.589 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 14891.4378ms.
2025-07-15 14:41:29.603 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 80.2006ms
2025-07-15 14:41:29.605 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:41:29.611 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 14922.7645ms
2025-07-15 14:41:29.613 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:41:29.615 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 200 null application/json; charset=utf-8 14936.8563ms
2025-07-15 14:41:31.643 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=rzeUhOYWOyEwleDDem_nxA - null null
2025-07-15 14:41:31.651 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:41:31.666 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:42:50.959 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:42:50.963 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:42:50.964 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 4.1085ms
2025-07-15 14:42:50.966 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:42:50.971 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:42:50.972 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:42:50.973 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:42:50.985 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:42:50.987 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:42:50.989 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:42:50.990 +03:00 [INF] Initializing hub connection...
2025-07-15 14:42:55.755 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:42:55.765 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:42:55.767 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:42:55.785 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 29.7182ms
2025-07-15 14:42:55.785 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 4798.3352ms.
2025-07-15 14:42:55.792 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:42:55.794 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 4818.7415ms
2025-07-15 14:42:55.796 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:42:55.797 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 200 null application/json; charset=utf-8 4831.4499ms
2025-07-15 14:42:57.812 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=A_3S4g6KniUEmx9tpIdF7g - null null
2025-07-15 14:42:57.817 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:42:57.827 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:43:43.176 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:43:43.181 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:43:43.186 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 9.6087ms
2025-07-15 14:43:43.188 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:43:43.195 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:43:43.197 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:43:43.198 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:43:43.208 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:43:43.212 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:43:43.214 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:43:43.215 +03:00 [INF] Initializing hub connection...
2025-07-15 14:43:45.656 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:43:48.237 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:43:48.239 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:43:48.259 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 2604.261ms
2025-07-15 14:43:48.260 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 5048.4103ms.
2025-07-15 14:43:48.267 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:43:48.268 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 5067.9352ms
2025-07-15 14:43:48.271 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:43:48.273 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 200 null application/json; charset=utf-8 5085.2303ms
2025-07-15 14:43:50.288 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=TEgXWu3Ftiw3F6SR9YD3ew - null null
2025-07-15 14:43:50.292 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:43:50.303 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:43:59.384 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:43:59.390 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:43:59.391 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.365ms
2025-07-15 14:43:59.393 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:43:59.399 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:43:59.401 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:43:59.402 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:43:59.412 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:43:59.414 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:43:59.416 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:43:59.417 +03:00 [INF] Initializing hub connection...
2025-07-15 14:46:21.493 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:46:21.520 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:46:21.525 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:46:21.529 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:46:21.530 +03:00 [INF] Initializing hub connection...
2025-07-15 14:46:22.618 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:46:22.668 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:46:22.672 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:46:22.675 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:46:22.677 +03:00 [INF] Hosting environment: Production
2025-07-15 14:46:22.678 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:46:23.791 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:46:23.822 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:46:23.904 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:46:23.907 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 120.1541ms
2025-07-15 14:46:26.006 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=iwKsRInerRmCGGu3DEch8Q - null null
2025-07-15 14:46:26.013 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:46:26.060 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:46:26.120 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:46:30.966 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:46:30.972 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:46:30.974 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 7.8644ms
2025-07-15 14:46:30.977 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:46:30.982 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:46:30.984 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:46:30.993 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:46:31.394 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:46:31.398 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:46:31.400 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:46:31.402 +03:00 [INF] Initializing hub connection...
2025-07-15 14:46:34.783 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:46:42.015 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:46:42.821 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:46:42.821 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 10550.9336ms.
2025-07-15 14:46:42.848 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 8065.2335ms
2025-07-15 14:46:42.855 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:46:42.871 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 11874.7565ms
2025-07-15 14:46:42.876 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:46:42.879 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 11901.5736ms
2025-07-15 14:46:44.878 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=AVFRUSy7melnXqQly3HOJA - null null
2025-07-15 14:46:44.881 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:46:45.425 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:46:45.430 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:46:45.431 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.9858ms
2025-07-15 14:46:45.433 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:46:45.439 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:46:45.441 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:46:45.443 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:46:45.452 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:46:45.454 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:46:45.456 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:46:45.457 +03:00 [INF] Initializing hub connection...
2025-07-15 14:47:50.604 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 65149.5036ms.
2025-07-15 14:47:50.769 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:47:50.782 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:47:50.863 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:47:50.864 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 14:47:50.868 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 65423.4533ms
2025-07-15 14:47:50.869 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:47:50.864 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 14:47:50.892 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=iwKsRInerRmCGGu3DEch8Q - 101 null null 84885.6526ms
2025-07-15 14:47:50.893 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:47:50.895 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 126.3019ms
2025-07-15 14:47:50.896 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=AVFRUSy7melnXqQly3HOJA - 101 null null 66018.2943ms
2025-07-15 14:47:50.902 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 200 null application/json; charset=utf-8 65469.5043ms
2025-07-15 14:47:52.925 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=RdiA0J5E1UlWgABb1yEOsQ - null null
2025-07-15 14:47:52.930 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:48:56.504 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:48:56.531 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:48:56.535 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:48:56.538 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:48:56.539 +03:00 [INF] Initializing hub connection...
2025-07-15 14:48:57.646 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:48:57.700 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:48:57.702 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:48:57.704 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:48:57.705 +03:00 [INF] Hosting environment: Production
2025-07-15 14:48:57.707 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:48:58.781 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:48:58.800 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:48:58.815 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:48:58.818 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 39.458ms
2025-07-15 14:49:00.961 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=9jaGyqJxxzOHfG_w8njEMQ - null null
2025-07-15 14:49:00.973 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:49:01.013 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:49:01.070 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:49:11.420 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:49:11.424 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:49:11.426 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.0787ms
2025-07-15 14:49:11.429 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:49:11.432 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:49:11.433 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:49:11.449 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:49:11.857 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:49:11.862 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:49:11.865 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:49:11.867 +03:00 [INF] Initializing hub connection...
2025-07-15 14:49:13.911 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:49:13.920 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:49:13.922 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:49:13.988 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 76.86ms
2025-07-15 14:49:14.979 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3115.4568ms.
2025-07-15 14:49:14.990 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:49:15.024 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3569.4899ms
2025-07-15 14:49:15.027 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:49:15.029 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3599.4613ms
2025-07-15 14:49:16.016 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=pHMiAEy9LtBUQIZI669k0w - null null
2025-07-15 14:49:16.025 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:49:20.697 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:49:20.704 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:49:20.706 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 9.1121ms
2025-07-15 14:49:20.709 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:49:20.716 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:49:20.717 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:49:20.719 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:49:20.732 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:49:20.734 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:49:20.736 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:49:20.737 +03:00 [INF] Initializing hub connection...
2025-07-15 14:49:41.983 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:49:42.014 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 21252.7649ms.
2025-07-15 14:49:42.016 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:49:42.018 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:49:42.019 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:49:42.038 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 21316.7148ms
2025-07-15 14:49:42.038 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 93.1729ms
2025-07-15 14:49:42.040 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:49:42.043 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 21334.3459ms
2025-07-15 14:49:44.078 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=wne5WCKZJy8-gGt19CgWBw - null null
2025-07-15 14:49:44.083 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:49:44.095 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:53:27.425 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:53:27.516 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:53:27.540 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:53:27.551 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:53:27.554 +03:00 [INF] Initializing hub connection...
2025-07-15 14:53:29.951 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:53:30.060 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:53:30.068 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:53:30.076 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:53:30.078 +03:00 [INF] Hosting environment: Production
2025-07-15 14:53:30.082 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:53:30.188 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:53:30.235 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:53:30.288 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:53:30.299 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 120.3813ms
2025-07-15 14:53:31.519 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 14:53:31.531 +03:00 [INF] COM ports populated.
2025-07-15 14:53:32.469 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Rnn1Rdl3IyweXaBN0QLGKw - null null
2025-07-15 14:53:32.476 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:53:32.561 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:53:32.681 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:53:32.744 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-15 14:53:32.746 +03:00 [INF] TabPage returned successfully.
2025-07-15 14:53:42.509 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:53:42.518 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:53:42.521 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 11.8209ms
2025-07-15 14:53:42.527 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:53:42.535 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:53:42.538 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:53:42.555 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:53:42.918 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:53:42.923 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:53:42.925 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:53:42.926 +03:00 [INF] Initializing hub connection...
2025-07-15 14:53:43.031 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 104.9718ms.
2025-07-15 14:53:43.047 +03:00 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:53:43.096 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 534.7575ms
2025-07-15 14:53:43.101 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:53:43.105 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 500 null application/json; charset=utf-8 578.2547ms
2025-07-15 14:53:45.061 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:53:45.068 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:53:45.071 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:53:45.121 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 60.6494ms
2025-07-15 14:53:47.143 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=bduHVrOTTYoaz-49MpvMkA - null null
2025-07-15 14:53:47.149 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:54:06.296 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:54:06.301 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:54:06.303 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.7032ms
2025-07-15 14:54:06.305 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:54:06.312 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:54:06.314 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:54:06.316 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:54:06.335 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:54:06.336 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:54:06.340 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:54:06.341 +03:00 [INF] Initializing hub connection...
2025-07-15 14:54:06.478 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 141.5187ms.
2025-07-15 14:54:06.482 +03:00 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:54:06.485 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 166.1002ms
2025-07-15 14:54:06.487 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:54:06.489 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 500 null application/json; charset=utf-8 183.1752ms
2025-07-15 14:54:08.526 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:54:08.533 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:54:08.535 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:54:08.603 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 76.6235ms
2025-07-15 14:54:10.643 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=IxxREABpYp9llgtk2l9mBQ - null null
2025-07-15 14:54:10.649 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:54:10.666 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:54:22.700 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:54:22.726 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:54:22.753 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:54:22.757 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:54:22.759 +03:00 [INF] Initializing hub connection...
2025-07-15 14:54:23.810 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:54:23.851 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:54:23.854 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:54:23.857 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:54:23.858 +03:00 [INF] Hosting environment: Production
2025-07-15 14:54:23.860 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:54:25.026 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:54:25.043 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:54:25.059 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:54:25.061 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 38.2818ms
2025-07-15 14:54:27.182 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=_UbDrdhzzUA_D_sb3UCw4w - null null
2025-07-15 14:54:27.193 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:54:27.235 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:54:27.290 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:54:31.940 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:54:31.950 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:54:31.953 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 13.6935ms
2025-07-15 14:54:31.957 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:54:31.961 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:54:31.963 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:54:31.971 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:54:32.378 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:54:32.383 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:54:32.387 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:54:32.388 +03:00 [INF] Initializing hub connection...
2025-07-15 14:54:42.062 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:54:42.097 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 9712.6226ms.
2025-07-15 14:54:42.099 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:54:42.103 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:54:42.127 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 68.867ms
2025-07-15 14:54:42.132 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:54:42.156 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 10180.3942ms
2025-07-15 14:54:42.159 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:54:42.161 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 10203.8718ms
2025-07-15 14:54:44.175 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=K3KtxBV-LnmF0ZVk9-60ww - null null
2025-07-15 14:54:44.176 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:55:17.877 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:55:17.983 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:55:17.991 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:55:17.996 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:55:17.997 +03:00 [INF] Initializing hub connection...
2025-07-15 14:55:19.965 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:55:20.052 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:55:20.056 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:55:20.060 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:55:20.067 +03:00 [INF] Hosting environment: Production
2025-07-15 14:55:20.079 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:55:20.410 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:55:20.456 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:55:20.485 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:55:20.493 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 90.2617ms
2025-07-15 14:55:22.661 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=WEMxWfmfopQgD72uaNDq4w - null null
2025-07-15 14:55:22.666 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:55:22.716 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:55:22.784 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:55:24.259 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:55:24.271 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:55:24.273 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 14.1177ms
2025-07-15 14:55:24.278 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:55:24.281 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:55:24.283 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:55:24.303 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:55:24.717 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:55:24.721 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:55:24.724 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:55:24.726 +03:00 [INF] Initializing hub connection...
2025-07-15 14:55:29.663 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:55:29.729 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 4837.1918ms.
2025-07-15 14:55:29.737 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:55:29.741 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:55:29.743 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 112.5378ms
2025-07-15 14:55:29.744 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:55:29.766 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 5458.9804ms
2025-07-15 14:55:29.768 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:55:29.770 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 5492.3295ms
2025-07-15 14:55:32.392 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=BMuRBWBf27Sfbu9Oll6WXg - null null
2025-07-15 14:55:32.404 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:57:30.831 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:57:30.941 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:57:30.953 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:57:30.965 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:57:30.969 +03:00 [INF] Initializing hub connection...
2025-07-15 14:57:33.856 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:57:34.020 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:57:34.025 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:57:34.032 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:57:34.034 +03:00 [INF] Hosting environment: Production
2025-07-15 14:57:34.038 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:57:34.139 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:57:34.219 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:57:34.257 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:57:34.265 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 137.7176ms
2025-07-15 14:57:36.513 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Y_pOVuBbp_TuMqCvtw_rGw - null null
2025-07-15 14:57:36.523 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:57:36.533 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:57:36.557 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:57:36.562 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 28.8149ms
2025-07-15 14:57:36.573 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:57:36.583 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:57:36.587 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:57:36.608 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:57:36.609 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:57:36.714 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:57:37.110 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:57:37.119 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:57:37.122 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:57:37.124 +03:00 [INF] Initializing hub connection...
2025-07-15 14:57:39.448 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:57:39.485 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:57:39.596 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:57:39.609 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 177.7285ms
2025-07-15 14:57:40.253 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3133.7492ms.
2025-07-15 14:57:40.314 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:57:40.354 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3734.5316ms
2025-07-15 14:57:40.365 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:57:40.367 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3794.5142ms
2025-07-15 14:57:43.041 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=LLhXrOEJauNzd7RqtqKStQ - null null
2025-07-15 14:57:43.044 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:57:58.299 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:57:58.363 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:57:58.372 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:57:58.377 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:57:58.379 +03:00 [INF] Initializing hub connection...
2025-07-15 14:58:00.357 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:58:00.434 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:58:00.441 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:58:00.446 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:58:00.449 +03:00 [INF] Hosting environment: Production
2025-07-15 14:58:00.450 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:58:00.824 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:58:00.858 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:58:00.884 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:58:00.889 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 70.7037ms
2025-07-15 14:58:03.036 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=HILaAJBoh1O805daaD1yfQ - null null
2025-07-15 14:58:03.050 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:58:03.126 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 14:58:03.199 +03:00 [INF] Barcode initialized successfully.
2025-07-15 14:58:04.851 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:58:04.859 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:58:04.863 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 11.9161ms
2025-07-15 14:58:04.867 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:58:04.872 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:58:04.873 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:58:04.883 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:58:05.272 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:58:05.279 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:58:05.282 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:58:05.283 +03:00 [INF] Initializing hub connection...
2025-07-15 14:58:07.881 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:58:07.921 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:58:07.923 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:58:07.976 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 95.9016ms
2025-07-15 14:58:08.375 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 3094.7226ms.
2025-07-15 14:58:08.384 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 14:58:08.432 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 3542.8351ms
2025-07-15 14:58:08.436 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:58:08.438 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 3571.5633ms
2025-07-15 14:58:10.004 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Y3E-QpTMDtlOMht2uUTPrw - null null
2025-07-15 14:58:10.012 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 14:58:25.273 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:58:25.276 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:58:25.278 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.3165ms
2025-07-15 14:58:25.280 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 14:58:25.287 +03:00 [INF] CORS policy execution successful.
2025-07-15 14:58:25.289 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 14:58:25.295 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 14:58:25.307 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 14:58:25.310 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 14:58:25.313 +03:00 [INF] Capture image mode set to: true
2025-07-15 14:58:25.314 +03:00 [INF] Initializing hub connection...
2025-07-15 14:59:56.045 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 14:59:56.068 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 14:59:56.096 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 14:59:56.100 +03:00 [INF] Capture image mode set to: false
2025-07-15 14:59:56.101 +03:00 [INF] Initializing hub connection...
2025-07-15 14:59:57.199 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 14:59:57.251 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 14:59:57.255 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 14:59:57.259 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 14:59:57.261 +03:00 [INF] Hosting environment: Production
2025-07-15 14:59:57.263 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 14:59:58.372 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 14:59:58.409 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 14:59:58.432 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 14:59:58.435 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 70.6992ms
2025-07-15 15:00:00.603 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=UVPgzoYf3zPCklw3iXc4TA - null null
2025-07-15 15:00:00.612 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:00:00.656 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 15:00:00.717 +03:00 [INF] Barcode initialized successfully.
2025-07-15 15:00:02.770 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:00:02.778 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:00:02.781 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 11.0817ms
2025-07-15 15:00:02.789 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:00:02.793 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:00:02.795 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:00:02.809 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 15:00:03.311 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 15:00:03.317 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 15:00:03.322 +03:00 [INF] Capture image mode set to: true
2025-07-15 15:00:03.324 +03:00 [INF] Initializing hub connection...
2025-07-15 15:01:59.771 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 15:01:59.858 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 15:01:59.872 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 15:01:59.885 +03:00 [INF] Capture image mode set to: false
2025-07-15 15:01:59.889 +03:00 [INF] Initializing hub connection...
2025-07-15 15:02:03.065 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 15:02:03.275 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 15:02:03.284 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 15:02:03.294 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 15:02:03.300 +03:00 [INF] Hosting environment: Production
2025-07-15 15:02:03.304 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 15:02:03.614 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 15:02:03.691 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 15:02:03.742 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 15:02:03.751 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 152.9095ms
2025-07-15 15:02:06.001 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=rxiOMNOQ6qOV7tWNG4v4kw - null null
2025-07-15 15:02:06.013 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:02:06.098 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 15:02:06.220 +03:00 [INF] Barcode initialized successfully.
2025-07-15 15:02:12.947 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:02:12.959 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:02:12.961 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 13.9357ms
2025-07-15 15:02:12.966 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:02:12.972 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:02:12.974 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:02:12.998 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 15:02:13.444 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 15:02:13.451 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 15:02:13.454 +03:00 [INF] Capture image mode set to: true
2025-07-15 15:02:13.455 +03:00 [INF] Initializing hub connection...
2025-07-15 15:06:07.176 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 15:06:07.201 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 15:06:07.208 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 15:06:07.212 +03:00 [INF] Capture image mode set to: false
2025-07-15 15:06:07.214 +03:00 [INF] Initializing hub connection...
2025-07-15 15:06:08.266 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 15:06:08.343 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 15:06:08.347 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 15:06:08.350 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 15:06:08.352 +03:00 [INF] Hosting environment: Production
2025-07-15 15:06:08.354 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 15:06:09.435 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 15:06:09.455 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 15:06:09.468 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 15:06:09.488 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 55.2698ms
2025-07-15 15:06:11.557 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=cNgmP6k5Dlc1QaIBBNxfrw - null null
2025-07-15 15:06:11.563 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:06:11.634 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 15:06:11.691 +03:00 [INF] Barcode initialized successfully.
2025-07-15 15:06:16.223 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:06:16.232 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:06:16.234 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 10.892ms
2025-07-15 15:06:16.238 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:06:16.243 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:06:16.244 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:06:16.254 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 15:06:16.658 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 15:06:16.665 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 15:06:16.667 +03:00 [INF] Capture image mode set to: true
2025-07-15 15:06:16.669 +03:00 [INF] Initializing hub connection...
2025-07-15 15:07:10.103 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 15:07:10.150 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 53441.1662ms.
2025-07-15 15:07:10.151 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 15:07:10.158 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 15:07:10.171 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-15 15:07:10.184 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 120.6498ms
2025-07-15 15:07:10.186 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=cNgmP6k5Dlc1QaIBBNxfrw - 101 null null 58629.097ms
2025-07-15 15:07:10.187 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 15:07:10.210 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 53949.7654ms
2025-07-15 15:07:10.213 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:07:10.215 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 53976.9038ms
2025-07-15 15:07:12.224 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Pd4MUwx4BKibZe-t0FsEPw - null null
2025-07-15 15:07:12.228 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:40:54.065 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 15:40:54.201 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-15 15:40:54.216 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-15 15:40:54.229 +03:00 [INF] Capture image mode set to: false
2025-07-15 15:40:54.234 +03:00 [INF] Initializing hub connection...
2025-07-15 15:40:58.149 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 15:40:58.345 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 15:40:58.352 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 15:40:58.365 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 15:40:58.371 +03:00 [INF] Hosting environment: Production
2025-07-15 15:40:58.374 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 15:40:58.534 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 15:40:58.607 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 15:40:58.650 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 15:40:58.658 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 138.6167ms
2025-07-15 15:41:00.909 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=QhUbfZNkB6j4fKvR0UxIGg - null null
2025-07-15 15:41:00.923 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:41:01.146 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-15 15:41:01.303 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:04:55.529 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:04:55.601 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:04:55.611 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:04:55.619 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:04:55.621 +03:00 [INF] Initializing hub connection...
2025-07-15 16:04:57.710 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:04:57.813 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:04:57.817 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:04:57.823 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:04:57.826 +03:00 [INF] Hosting environment: Production
2025-07-15 16:04:57.829 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:04:58.033 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:04:58.079 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:04:58.109 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:04:58.118 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 93.6051ms
2025-07-15 16:05:00.269 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=jvDFcwYtmiddHhk7qtYYCg - null null
2025-07-15 16:05:00.278 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:05:00.396 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:05:00.515 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:05:19.628 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:05:19.635 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:05:19.638 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 10.145ms
2025-07-15 16:05:19.644 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:05:19.648 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:05:19.649 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:05:19.663 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:05:20.072 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:05:20.077 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:05:20.079 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:05:20.080 +03:00 [INF] Initializing hub connection...
2025-07-15 16:05:22.147 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:05:22.150 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:05:22.151 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:05:22.195 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 48.1875ms
2025-07-15 16:05:22.197 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 2118.9726ms.
2025-07-15 16:05:22.210 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:05:22.232 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2564.2013ms
2025-07-15 16:05:22.235 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:05:22.237 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2593.0603ms
2025-07-15 16:05:24.227 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=VDYl9VR4R_dSvkufGlaAzQ - null null
2025-07-15 16:05:24.232 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:10:51.331 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:10:51.470 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:10:51.485 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:10:51.499 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:10:51.503 +03:00 [INF] Initializing hub connection...
2025-07-15 16:10:54.805 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:10:54.980 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:10:54.984 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:10:54.994 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:10:54.997 +03:00 [INF] Hosting environment: Production
2025-07-15 16:10:55.000 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:10:55.424 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:10:55.509 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:10:55.549 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:10:55.559 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 151.966ms
2025-07-15 16:10:57.788 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=hwOQswNROps2X4r5FgnDHg - null null
2025-07-15 16:10:57.799 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:10:57.877 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:10:57.959 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:11:04.512 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:11:04.521 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:11:04.523 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 10.977ms
2025-07-15 16:11:04.527 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:11:04.533 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:11:04.535 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:11:04.552 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:11:04.964 +03:00 [INF] Executing action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) - Validation state: "Valid"
2025-07-15 16:11:15.063 +03:00 [INF] Executed action method Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 10086.7124ms.
2025-07-15 16:11:15.096 +03:00 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:11:15.106 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 16:11:15.108 +03:00 [INF] COM ports populated.
2025-07-15 16:11:15.166 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-15 16:11:15.167 +03:00 [INF] TabPage returned successfully.
2025-07-15 16:11:15.172 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 10613.1216ms
2025-07-15 16:11:15.174 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:11:15.178 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 500 null application/json; charset=utf-8 10651.408ms
2025-07-15 16:11:30.781 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:11:30.807 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:11:30.810 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:11:30.814 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:11:30.818 +03:00 [INF] Initializing hub connection...
2025-07-15 16:11:31.673 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-15 16:11:31.733 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:11:31.736 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:11:31.740 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:11:31.741 +03:00 [INF] Hosting environment: Production
2025-07-15 16:11:31.744 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-15 16:11:32.955 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:11:32.983 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:11:32.994 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:11:32.999 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 45.1985ms
2025-07-15 16:11:35.049 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=j9j-hyCC4tmfN-JxBPfw8A - null null
2025-07-15 16:11:35.056 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:11:35.094 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:11:35.147 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:11:37.564 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:11:37.573 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:11:37.578 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 13.6712ms
2025-07-15 16:11:37.583 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:11:37.588 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:11:37.590 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:11:37.602 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:11:38.109 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:11:38.113 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:11:38.116 +03:00 [INF] Initializing hub connection...
2025-07-15 16:11:40.153 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:11:40.157 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:11:40.173 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:11:40.176 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:11:40.178 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 25.1609ms
2025-07-15 16:11:40.210 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2593.0009ms
2025-07-15 16:11:40.213 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:11:40.219 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2636.0857ms
2025-07-15 16:11:42.204 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=bdp2uBWpBPSQpid_BEBAqg - null null
2025-07-15 16:11:42.209 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:11:48.240 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:11:48.245 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:11:48.246 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.9081ms
2025-07-15 16:11:48.248 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:11:48.254 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:11:48.255 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:11:48.257 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:11:48.260 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:11:48.262 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:11:48.264 +03:00 [INF] Initializing hub connection...
2025-07-15 16:11:50.294 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:11:50.299 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:11:50.303 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2043.0927ms
2025-07-15 16:11:50.309 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:11:50.310 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:11:50.312 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:11:50.313 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2064.8758ms
2025-07-15 16:11:50.315 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 15.9289ms
2025-07-15 16:11:52.340 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=BfW61fUzGB-0rD-IaIrP-w - null null
2025-07-15 16:11:52.344 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:11:52.370 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
