2025-07-15 15:56:10.194 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 15:56:10.339 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-15 15:56:10.354 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-15 15:56:10.366 +03:00 [INF] Capture image mode set to: false
2025-07-15 15:56:10.371 +03:00 [INF] Initializing hub connection...
2025-07-15 15:56:13.018 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\wwwroot. Static files may be unavailable.
2025-07-15 15:56:13.136 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 15:56:13.144 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 15:56:13.155 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 15:56:13.161 +03:00 [INF] Hosting environment: Production
2025-07-15 15:56:13.165 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent
2025-07-15 15:56:13.257 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 15:56:13.327 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 15:56:13.373 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 15:56:13.384 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 129.9426ms
2025-07-15 15:56:15.458 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=86qWA8SCveOMWz6UjNXOgw - null null
2025-07-15 15:56:15.471 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:56:15.589 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-15 15:56:15.659 +03:00 [INF] Barcode initialized successfully.
2025-07-15 15:56:24.521 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:56:24.526 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:56:24.528 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.185ms
2025-07-15 15:56:24.531 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:56:24.533 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:56:24.534 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:56:24.541 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 15:56:32.649 +03:00 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 15:56:32.667 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 8122.83ms
2025-07-15 15:56:32.669 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:56:32.670 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 500 null application/json; charset=utf-8 8138.701ms
2025-07-15 15:56:36.189 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 15:56:36.191 +03:00 [INF] COM ports populated.
2025-07-15 15:56:37.311 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-15 15:56:37.312 +03:00 [INF] TabPage returned successfully.
2025-07-15 15:56:46.001 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 15:56:46.034 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 15:56:46.037 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 15:56:46.040 +03:00 [INF] Capture image mode set to: false
2025-07-15 15:56:46.042 +03:00 [INF] Initializing hub connection...
2025-07-15 15:57:13.857 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 15:57:13.934 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 15:57:13.942 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 15:57:13.949 +03:00 [INF] Capture image mode set to: false
2025-07-15 15:57:13.951 +03:00 [INF] Initializing hub connection...
