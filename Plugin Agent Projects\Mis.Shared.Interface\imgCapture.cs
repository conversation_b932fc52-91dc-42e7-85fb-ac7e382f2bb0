﻿using System;
using System.Drawing;
using System.Text;
using System.IO;

namespace Mis.Shared.Interface
{
    public enum HoneywellModel
    {
        Model1950,
        Model1900,
        Auto // Auto-detect based on data structure
    }

    public static class imgCapture
    {
        // Honeywell 1950 commands
        const string hw1950ImgsnpCmd = "IMGSNP2G1B1L";//أمر التصوير
        const string hw1950ImgshpCmd = "IMGSHP2P0L843R639B0T0M8D1S6F";//أمر جلب الصورة من القارئ
        public readonly static string hw1950PictureCmd = string.Format("{0};{1}.", hw1950ImgsnpCmd, hw1950ImgshpCmd);//جمع الأمرين مع بعض
        private readonly static int honeyWell1950PictureCmdLength = hw1950PictureCmd.Length; //طول الأمر

        // Honeywell 1900 commands
        const string hw1900ImgsnpCmd = "IMGSNP";//أمر التصوير للموديل 1900
        const string hw1900ImgshpCmd = "IMGSHP";//أمر جلب الصورة من القارئ للموديل 1900
        public readonly static string hw1900PictureCmd = string.Format("{0};{1}.", hw1900ImgsnpCmd, hw1900ImgshpCmd);//جمع الأمرين مع بعض
        private readonly static int honeyWell1900PictureCmdLength = hw1900PictureCmd.Length; //طول الأمر

        // Legacy support - defaults to 1950 for backward compatibility
        public readonly static string hwPictureCmd = hw1950PictureCmd;
        private readonly static int honeyWellPictureCmdLength = honeyWell1950PictureCmdLength;
        public static Image GetDeviceScannerImage(byte[] imageArray)
        {
            // Check if imageArray is null or empty
            if (imageArray == null || imageArray.Length < 1)
            {
                Console.WriteLine("Image array is null or empty.");
                return null; // Not enough data for the image
            }

            // Attempt to create an image directly from the byte array
            try
            {
                using (MemoryStream ms = new MemoryStream(imageArray))
                {
                    Image img = Image.FromStream(ms);
                    Console.WriteLine("Image successfully created from byte array.");
                    return img; // Return the image if successful
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting byte array to image: {ex.Message}");
                return null; // Return null if there's an error
            }
        }



        public static Image GetbarcodeScannerImage(byte[] imageArray, HoneywellModel model = HoneywellModel.Auto)
        {
            return GetbarcodeScannerImageInternal(imageArray, model);
        }

        // Legacy method for backward compatibility
        public static Image GetbarcodeScannerImage(byte[] imageArray)
        {
            return GetbarcodeScannerImageInternal(imageArray, HoneywellModel.Auto);
        }

        private static Image GetbarcodeScannerImageInternal(byte[] imageArray, HoneywellModel model)
        {
            Console.WriteLine($"[DEBUG] GetbarcodeScannerImageInternal called with model: {model}");

            if (imageArray == null || imageArray.Length < 10)
            {
                Console.WriteLine("[DEBUG] Image array is null or too small.");
                return null;
            }

            Console.WriteLine($"[DEBUG] Image array length: {imageArray.Length} bytes");
            LogByteArrayHeader(imageArray, 50); // Log first 50 bytes for debugging

            // Auto-detect model if not specified
            if (model == HoneywellModel.Auto)
            {
                model = DetectHoneywellModel(imageArray);
                Console.WriteLine($"[DEBUG] Auto-detected Honeywell model: {model}");
            }
            else
            {
                Console.WriteLine($"[DEBUG] Using specified model: {model}");
            }

            int startIndex = FindImageDataStart(imageArray, model);
            if (startIndex == -1)
            {
                Console.WriteLine("[DEBUG] Could not find image data start marker.");
                return null;
            }
            Console.WriteLine($"[DEBUG] Found image data start at index: {startIndex}");

            int cmdLength = GetCommandLength(model);
            Console.WriteLine($"[DEBUG] Command length for {model}: {cmdLength}");

            int imageDataLength = CalculateImageDataLength(imageArray, startIndex, cmdLength);
            Console.WriteLine($"[DEBUG] Calculated image data length: {imageDataLength}");

            if (imageDataLength <= 0)
            {
                Console.WriteLine("[DEBUG] Invalid image data length calculated.");
                return null;
            }

            Image img = null;
            try
            {
                Console.WriteLine($"[DEBUG] Attempting to create image from stream: startIndex={startIndex}, length={imageDataLength}");
                LogByteArraySegment(imageArray, startIndex, Math.Min(20, imageDataLength)); // Log first 20 bytes of image data

                using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageDataLength))
                {
                    img = Image.FromStream(ms);
                    Console.WriteLine($"[DEBUG] ✓ Successfully created image from {model} data. Size: {img.Width}x{img.Height}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] ✗ Error converting byte array to image for {model}: {ex.Message}");
                Console.WriteLine($"[DEBUG] Exception type: {ex.GetType().Name}");

                // Try alternative parsing method
                Console.WriteLine($"[DEBUG] Attempting alternative parsing methods...");
                img = TryAlternativeImageParsing(imageArray, startIndex, model);
            }

            return img;
        }

        public static string GetImageAsBase64(byte[] imageArray, HoneywellModel model = HoneywellModel.Auto)
        {
            Image img = GetbarcodeScannerImageInternal(imageArray, model);
            if (img == null)
            {
                return null;
            }

            try
            {
                using (MemoryStream msBase64 = new MemoryStream())
                {
                    img.Save(msBase64, System.Drawing.Imaging.ImageFormat.Png);
                    byte[] imgBytes = msBase64.ToArray();
                    return Convert.ToBase64String(imgBytes);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting image to Base64: {ex.Message}");
                return null;
            }
            finally
            {
                img?.Dispose();
            }
        }

        // Legacy method for backward compatibility
        public static string GetImageAsBase64(byte[] imageArray)
        {
            return GetImageAsBase64(imageArray, HoneywellModel.Auto);
        }

        #region Helper Methods

        private static HoneywellModel DetectHoneywellModel(byte[] imageArray)
        {
            // Check for 1950 specific patterns
            if (ContainsPattern(imageArray, hw1950ImgsnpCmd) || ContainsPattern(imageArray, hw1950ImgshpCmd))
            {
                return HoneywellModel.Model1950;
            }

            // Check for 1900 specific patterns
            if (ContainsPattern(imageArray, hw1900ImgsnpCmd) || ContainsPattern(imageArray, hw1900ImgshpCmd))
            {
                return HoneywellModel.Model1900;
            }

            // Check data structure patterns
            // 1900 series typically has different header structure
            if (imageArray.Length > 20)
            {
                // Look for JPEG/PNG headers after different start markers
                for (int i = 0; i < Math.Min(50, imageArray.Length - 10); i++)
                {
                    // Check for JPEG header (FF D8 FF)
                    if (imageArray[i] == 0xFF && imageArray[i + 1] == 0xD8 && imageArray[i + 2] == 0xFF)
                    {
                        // If JPEG header is found early, likely 1900 series
                        if (i < 20) return HoneywellModel.Model1900;
                        // If found later, likely 1950 series
                        return HoneywellModel.Model1950;
                    }
                    // Check for PNG header (89 50 4E 47)
                    if (imageArray[i] == 0x89 && imageArray[i + 1] == 0x50 &&
                        imageArray[i + 2] == 0x4E && imageArray[i + 3] == 0x47)
                    {
                        if (i < 20) return HoneywellModel.Model1900;
                        return HoneywellModel.Model1950;
                    }
                }
            }

            // Default to 1950 for backward compatibility
            return HoneywellModel.Model1950;
        }

        private static bool ContainsPattern(byte[] array, string pattern)
        {
            byte[] patternBytes = Encoding.ASCII.GetBytes(pattern);
            for (int i = 0; i <= array.Length - patternBytes.Length; i++)
            {
                bool match = true;
                for (int j = 0; j < patternBytes.Length; j++)
                {
                    if (array[i + j] != patternBytes[j])
                    {
                        match = false;
                        break;
                    }
                }
                if (match) return true;
            }
            return false;
        }

        private static int FindImageDataStart(byte[] imageArray, HoneywellModel model)
        {
            // Try different start markers based on model
            byte[] startMarkers = model switch
            {
                HoneywellModel.Model1900 => new byte[] { 0x1d, 0x02, 0x16 }, // Common 1900 markers
                HoneywellModel.Model1950 => new byte[] { 0x1d }, // 1950 marker
                _ => new byte[] { 0x1d, 0x02, 0x16 } // Auto-detect: try multiple
            };

            foreach (byte marker in startMarkers)
            {
                for (int i = 0; i < imageArray.Length; i++)
                {
                    if (imageArray[i] == marker)
                    {
                        return i + 1; // Return position after marker
                    }
                }
            }

            // If no marker found, try to find image headers directly
            return FindImageHeaderStart(imageArray);
        }

        private static int FindImageHeaderStart(byte[] imageArray)
        {
            for (int i = 0; i < imageArray.Length - 4; i++)
            {
                // Look for JPEG header (FF D8 FF)
                if (imageArray[i] == 0xFF && imageArray[i + 1] == 0xD8 && imageArray[i + 2] == 0xFF)
                {
                    return i;
                }
                // Look for PNG header (89 50 4E 47)
                if (imageArray[i] == 0x89 && imageArray[i + 1] == 0x50 &&
                    imageArray[i + 2] == 0x4E && imageArray[i + 3] == 0x47)
                {
                    return i;
                }
            }
            return -1; // No image header found
        }

        private static int GetCommandLength(HoneywellModel model)
        {
            return model switch
            {
                HoneywellModel.Model1900 => honeyWell1900PictureCmdLength,
                HoneywellModel.Model1950 => honeyWell1950PictureCmdLength,
                _ => honeyWell1950PictureCmdLength // Default
            };
        }

        private static int CalculateImageDataLength(byte[] imageArray, int startIndex, int cmdLength)
        {
            // For 1900 series, the image data might not have the command suffix
            // Try to find the actual end of image data
            int endIndex = imageArray.Length;

            // Look for image end markers (JPEG: FF D9, PNG: IEND)
            for (int i = startIndex + 10; i < imageArray.Length - 1; i++)
            {
                // JPEG end marker
                if (imageArray[i] == 0xFF && imageArray[i + 1] == 0xD9)
                {
                    return i + 2 - startIndex;
                }
                // PNG end marker (simplified check)
                if (i < imageArray.Length - 8 &&
                    imageArray[i] == 0x49 && imageArray[i + 1] == 0x45 &&
                    imageArray[i + 2] == 0x4E && imageArray[i + 3] == 0x44)
                {
                    return i + 8 - startIndex;
                }
            }

            // Fallback: use original calculation but be more flexible
            int calculatedLength = imageArray.Length - startIndex - cmdLength - 2;
            if (calculatedLength > 0)
            {
                return calculatedLength;
            }

            // Last resort: use all remaining data
            return imageArray.Length - startIndex;
        }

        private static Image TryAlternativeImageParsing(byte[] imageArray, int startIndex, HoneywellModel model)
        {
            Console.WriteLine($"Trying alternative parsing for {model}");

            // Try different data lengths
            int[] lengthOffsets = { 0, -2, -4, -8, -16, 2, 4, 8, 16 };

            foreach (int offset in lengthOffsets)
            {
                try
                {
                    int length = imageArray.Length - startIndex + offset;
                    if (length > 0 && startIndex + length <= imageArray.Length)
                    {
                        using (MemoryStream ms = new MemoryStream(imageArray, startIndex, length))
                        {
                            Image img = Image.FromStream(ms);
                            Console.WriteLine($"Alternative parsing successful with offset {offset}");
                            return img;
                        }
                    }
                }
                catch
                {
                    // Continue trying other offsets
                }
            }

            // Try parsing from different start positions
            for (int newStart = Math.Max(0, startIndex - 10); newStart <= startIndex + 10 && newStart < imageArray.Length; newStart++)
            {
                try
                {
                    int length = imageArray.Length - newStart;
                    if (length > 0)
                    {
                        using (MemoryStream ms = new MemoryStream(imageArray, newStart, length))
                        {
                            Image img = Image.FromStream(ms);
                            Console.WriteLine($"Alternative parsing successful with start position {newStart}");
                            return img;
                        }
                    }
                }
                catch
                {
                    // Continue trying other positions
                }
            }

            Console.WriteLine("[DEBUG] All alternative parsing methods failed");
            return null;
        }

        private static void LogByteArrayHeader(byte[] array, int count)
        {
            if (array == null) return;

            int logCount = Math.Min(count, array.Length);
            var hexString = string.Join(" ", array.Take(logCount).Select(b => b.ToString("X2")));
            Console.WriteLine($"[DEBUG] First {logCount} bytes: {hexString}");

            // Also log as ASCII for readable characters
            var asciiString = string.Join("", array.Take(logCount).Select(b =>
                (b >= 32 && b <= 126) ? (char)b : '.'));
            Console.WriteLine($"[DEBUG] ASCII representation: {asciiString}");
        }

        private static void LogByteArraySegment(byte[] array, int startIndex, int count)
        {
            if (array == null || startIndex >= array.Length) return;

            int logCount = Math.Min(count, array.Length - startIndex);
            var segment = array.Skip(startIndex).Take(logCount);
            var hexString = string.Join(" ", segment.Select(b => b.ToString("X2")));
            Console.WriteLine($"[DEBUG] Bytes at index {startIndex} ({logCount} bytes): {hexString}");
        }

        #endregion

        #region Public Methods for Model-Specific Commands

        public static string GetPictureCmdForModel(HoneywellModel model)
        {
            return model switch
            {
                HoneywellModel.Model1900 => hw1900PictureCmd,
                HoneywellModel.Model1950 => hw1950PictureCmd,
                _ => hw1950PictureCmd // Default to 1950 for backward compatibility
            };
        }

        public static string GetImageSnapCmdForModel(HoneywellModel model)
        {
            return model switch
            {
                HoneywellModel.Model1900 => hw1900ImgsnpCmd,
                HoneywellModel.Model1950 => hw1950ImgsnpCmd,
                _ => hw1950ImgsnpCmd // Default to 1950 for backward compatibility
            };
        }

        public static string GetImageShipCmdForModel(HoneywellModel model)
        {
            return model switch
            {
                HoneywellModel.Model1900 => hw1900ImgshpCmd,
                HoneywellModel.Model1950 => hw1950ImgshpCmd,
                _ => hw1950ImgshpCmd // Default to 1950 for backward compatibility
            };
        }

        #endregion

    }

}
