using System;
using System.Drawing;
using System.Text;

namespace Mis.Shared.Interface
{
    public static class imgCapture
    {
        const string hwImgsnpCmd = "IMGSNP2G1B1L";//أمر التصوير
        const string hwImgshpCmd = "IMGSHP2P0L843R639B0T0M8D1S6F";//أمر جلب الصورة من القارئ
                                                                  // const string hwImgshpCmd = "IMGSHP8F75K26U";//أمر جلب الصورة من القارئ
        public readonly static string hwPictureCmd = string.Format("{0};{1}.", hwImgsnpCmd, hwImgshpCmd);//جمع الأمرين مع بعض
        private readonly static int honeyWellPictureCmdLength = hwPictureCmd.Length; //طول الأمر
        public static Image GetDeviceScannerImage(byte[] imageArray)
        {
            // Check if imageArray is null or empty
            if (imageArray == null || imageArray.Length < 1)
            {
                Console.WriteLine("Image array is null or empty.");
                return null; // Not enough data for the image
            }

            // Attempt to create an image directly from the byte array
            try
            {
                using (MemoryStream ms = new MemoryStream(imageArray))
                {
                    Image img = Image.FromStream(ms);
                    Console.WriteLine("Image successfully created from byte array.");
                    return img; // Return the image if successful
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting byte array to image: {ex.Message}");
                return null; // Return null if there's an error
            }
        }



        public static Image GetbarcodeScannerImage(byte[] imageArray)
        {
            int startIndex = 0;

            for (int i = 0; i < imageArray.Length; i++)
            {
                if (imageArray[i] == 0x1d) // Image data starts from byte value 0x1D
                {
                    startIndex = i + 1; // Increment to start from the next byte
                    break;
                }
            }

            // Ensure that we have enough bytes to read
            if (startIndex >= imageArray.Length || startIndex + honeyWellPictureCmdLength + 2 > imageArray.Length)
            {
                return null; // Not enough data for the image
            }

            Image img = null;
            try
            {
                using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex - honeyWellPictureCmdLength - 2))
                {
                    img = Image.FromStream(ms);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting byte array to image: {ex.Message}");
            }

            return img;
        }

        public static string GetImageAsBase64(byte[] imageArray)
        {
            int startIndex = 0;

            for (int i = 0; i < imageArray.Length; i++)
            {
                if (imageArray[i] == 0x1d) // Image data starts from byte value 0x1D
                {
                    startIndex = i + 1; // Increment to start from the next byte
                    break;
                }
            }

            // Ensure that we have enough bytes to read
            if (startIndex >= imageArray.Length || startIndex + honeyWellPictureCmdLength + 2 > imageArray.Length)
            {
                return null; // Not enough data for the image
            }

            try
            {
                using (MemoryStream ms = new MemoryStream(imageArray, startIndex, imageArray.Length - startIndex - honeyWellPictureCmdLength - 2))
                {
                    Image img = Image.FromStream(ms);
                    using (MemoryStream msBase64 = new MemoryStream())
                    {
                        img.Save(msBase64, System.Drawing.Imaging.ImageFormat.Png); // Save the image to a memory stream
                        byte[] imgBytes = msBase64.ToArray(); // Get the byte array of the image
                        return Convert.ToBase64String(imgBytes); // Convert to Base64 string
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting byte array to Base64: {ex.Message}");
                return null;
            }
        }

    }

}
