using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;

namespace Mis.Shared.Interface
{
    /// <summary>
    /// Test class to demonstrate the usage of the updated Honeywell barcode reader support
    /// </summary>
    public static class HoneywellBarcodeTest
    {
        /// <summary>
        /// Test method to demonstrate how to use the updated imgCapture class
        /// with different Honeywell models
        /// </summary>
        /// <param name="imageData">Raw image data from barcode reader</param>
        public static void TestImageCapture(byte[] imageData)
        {
            Console.WriteLine("=== Honeywell Barcode Reader Image Capture Test ===");

            if (imageData == null || imageData.Length == 0)
            {
                Console.WriteLine("Error: No image data provided");
                return;
            }

            Console.WriteLine($"Image data length: {imageData.Length} bytes");

            // Test 1: Auto-detection (recommended)
            Console.WriteLine("\n1. Testing with Auto-detection:");
            TestWithModel(imageData, HoneywellModel.Auto);

            // Test 2: Specific model - Honey<PERSON> 1950
            Console.WriteLine("\n2. Testing with Honey<PERSON> 1950:");
            TestWithModel(imageData, HoneywellModel.Model1950);

            // Test 3: Specific model - Honeywell 1900
            Console.WriteLine("\n3. Testing with Honeywell 1900:");
            TestWithModel(imageData, HoneywellModel.Model1900);

            // Test 4: Legacy method (for backward compatibility)
            Console.WriteLine("\n4. Testing legacy method:");
            TestLegacyMethod(imageData);
        }

        private static void TestWithModel(byte[] imageData, HoneywellModel model)
        {
            try
            {
                // Test image extraction
                Image img = imgCapture.GetbarcodeScannerImage(imageData, model);
                if (img != null)
                {
                    Console.WriteLine($"✓ Successfully extracted image with {model}");
                    Console.WriteLine($"  Image size: {img.Width}x{img.Height}");
                    img.Dispose();
                }
                else
                {
                    Console.WriteLine($"✗ Failed to extract image with {model}");
                }

                // Test Base64 conversion
                string base64 = imgCapture.GetImageAsBase64(imageData, model);
                if (!string.IsNullOrEmpty(base64))
                {
                    Console.WriteLine($"✓ Successfully converted to Base64 with {model}");
                    Console.WriteLine($"  Base64 length: {base64.Length} characters");
                }
                else
                {
                    Console.WriteLine($"✗ Failed to convert to Base64 with {model}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Exception with {model}: {ex.Message}");
            }
        }

        private static void TestLegacyMethod(byte[] imageData)
        {
            try
            {
                // Test legacy methods (should use auto-detection internally)
                Image img = imgCapture.GetbarcodeScannerImage(imageData);
                if (img != null)
                {
                    Console.WriteLine("✓ Legacy method successfully extracted image");
                    Console.WriteLine($"  Image size: {img.Width}x{img.Height}");
                    img.Dispose();
                }
                else
                {
                    Console.WriteLine("✗ Legacy method failed to extract image");
                }

                string base64 = imgCapture.GetImageAsBase64(imageData);
                if (!string.IsNullOrEmpty(base64))
                {
                    Console.WriteLine("✓ Legacy method successfully converted to Base64");
                    Console.WriteLine($"  Base64 length: {base64.Length} characters");
                }
                else
                {
                    Console.WriteLine("✗ Legacy method failed to convert to Base64");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Legacy method exception: {ex.Message}");
            }
        }

        /// <summary>
        /// Test method to demonstrate command generation for different models
        /// </summary>
        public static void TestCommandGeneration()
        {
            Console.WriteLine("\n=== Command Generation Test ===");

            foreach (HoneywellModel model in Enum.GetValues<HoneywellModel>())
            {
                if (model == HoneywellModel.Auto) continue;

                Console.WriteLine($"\n{model} Commands:");
                Console.WriteLine($"  Picture Command: {imgCapture.GetPictureCmdForModel(model)}");
                Console.WriteLine($"  Image Snap Command: {imgCapture.GetImageSnapCmdForModel(model)}");
                Console.WriteLine($"  Image Ship Command: {imgCapture.GetImageShipCmdForModel(model)}");
            }
        }

        /// <summary>
        /// Simulate testing with sample data
        /// </summary>
        public static void RunSimulatedTest()
        {
            Console.WriteLine("=== Simulated Test with Sample Data ===");

            // Create sample JPEG header data for testing
            byte[] sampleJpegData = CreateSampleJpegData();

            Console.WriteLine("Testing with simulated JPEG data:");
            TestImageCapture(sampleJpegData);

            // Test command generation
            TestCommandGeneration();
        }

        private static byte[] CreateSampleJpegData()
        {
            // Create a minimal JPEG-like structure for testing
            var data = new List<byte>();

            // Add some header bytes
            data.AddRange(new byte[] { 0x00, 0x01, 0x02 });

            // Add 1D marker (common start marker)
            data.Add(0x1D);

            // Add JPEG header
            data.AddRange(new byte[] { 0xFF, 0xD8, 0xFF, 0xE0 });

            // Add some dummy JPEG data
            for (int i = 0; i < 100; i++)
            {
                data.Add((byte)(i % 256));
            }

            // Add JPEG end marker
            data.AddRange(new byte[] { 0xFF, 0xD9 });

            // Add some trailing data
            data.AddRange(new byte[] { 0x00, 0x00 });

            return data.ToArray();
        }
    }
}
